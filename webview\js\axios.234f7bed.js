import { A as Axios } from "./base.649d38c6.js";
import { a as showToast$2 } from "./toast.866a5bf5.js";
import { C as CryptoJS } from "./crypto-js.7319a219.js";
import { v as v4, l as logger, Z as ZMQ_MESSAGE_TYPE } from "./index.f30fa4a1.js";
import { c as showToast$1 } from "./toastWidget.67d1d2c8.js";
var LoginType = /* @__PURE__ */ ((LoginType2) => {
  LoginType2[LoginType2["QRCODE"] = 0] = "QRCODE";
  LoginType2[LoginType2["PHONE_NUMBER"] = 1] = "PHONE_NUMBER";
  return LoginType2;
})(LoginType || {});
var QRCodeType = /* @__PURE__ */ ((QRCodeType2) => {
  QRCodeType2[QRCodeType2["LARK"] = 0] = "LARK";
  QRCodeType2[QRCodeType2["DINGTALK"] = 1] = "DINGTALK";
  QRCodeType2[QRCodeType2["WECHAT"] = 2] = "WECHAT";
  return QRCodeType2;
})(QRCodeType || {});
var PhoneType = /* @__PURE__ */ ((PhoneType2) => {
  PhoneType2[PhoneType2["PASSWORD"] = 0] = "PASSWORD";
  PhoneType2[PhoneType2["VERIFYCODE"] = 1] = "VERIFYCODE";
  return PhoneType2;
})(PhoneType || {});
var RESPONSE_BUSINESS_STATUS_ENUM = /* @__PURE__ */ ((RESPONSE_BUSINESS_STATUS_ENUM2) => {
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["SUCCESS"] = 200] = "SUCCESS";
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["BUSINESS_ERROR"] = 400] = "BUSINESS_ERROR";
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["SERVER_ERROR"] = 500] = "SERVER_ERROR";
  RESPONSE_BUSINESS_STATUS_ENUM2["INVALID_LOGIN"] = "LOGIN_ERROR";
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["SERVER_INFO"] = 40001] = "SERVER_INFO";
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["TIMETABLE_NOT_CONFIG"] = 20001] = "TIMETABLE_NOT_CONFIG";
  RESPONSE_BUSINESS_STATUS_ENUM2[RESPONSE_BUSINESS_STATUS_ENUM2["TODAY_NO_CLASS"] = 20002] = "TODAY_NO_CLASS";
  return RESPONSE_BUSINESS_STATUS_ENUM2;
})(RESPONSE_BUSINESS_STATUS_ENUM || {});
var Menu_ENUM = /* @__PURE__ */ ((Menu_ENUM2) => {
  Menu_ENUM2[Menu_ENUM2["CLASS_BIND"] = 0] = "CLASS_BIND";
  Menu_ENUM2[Menu_ENUM2["TEACHER_ASSISTANT_SETTING"] = 1] = "TEACHER_ASSISTANT_SETTING";
  Menu_ENUM2[Menu_ENUM2["DEVICE_DEBUG"] = 2] = "DEVICE_DEBUG";
  Menu_ENUM2[Menu_ENUM2["ABOUT"] = 3] = "ABOUT";
  return Menu_ENUM2;
})(Menu_ENUM || {});
const SecretKey = "8OycNAk5";
const AKey = "zhhb";
const uuid = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === "x" ? r : r & 3 | 8;
    return v.toString(16);
  });
};
function md5(str, flg) {
  return CryptoJS.MD5(str).toString(flg ? CryptoJS.enc.Base64 : 0);
}
function HmacSHA256(message, key, flg) {
  return CryptoJS.HmacSHA256(message, key).toString(flg ? CryptoJS.enc.Base64 : 0);
}
function getDate(dateStr) {
  if (dateStr && typeof dateStr === "string") {
    let date = "";
    let [Y, M, D, h, m, s] = dateStr.replace(/[^0-9]+/g, ",").split(",");
    h = h || "00";
    m = m || "00";
    s = s || "00";
    if (Y && M && D) {
      date += [Y, M, D].join("/");
    }
    if (h && m && s) {
      date += ` ${[h, m, s].join(":")}`;
    }
    return new Date(date);
  }
  return /* @__PURE__ */ new Date();
}
function toDate(date) {
  if (typeof date === "string") {
    return getDate(date);
  }
  if (typeof date === "number") {
    return new Date(date);
  }
  return date || /* @__PURE__ */ new Date();
}
function formatDate(date, formatStr = "YYYY-MM-DD") {
  try {
    date = toDate(date);
    let str = formatStr;
    const Week = ["日", "一", "二", "三", "四", "五", "六"];
    const year = date.getFullYear();
    str = str.replace(/yyyy|YYYY/, `${year}`);
    str = str.replace(/yy|YY/, String(year).padStart(2, "0"));
    const month = date.getMonth() + 1;
    str = str.replace(/MM/, month > 9 ? String(month) : String(month).padStart(2, "0"));
    str = str.replace(/M/g, `${month}`);
    str = str.replace(/w|W/g, Week[date.getDay()]);
    str = str.replace(
      /dd|DD/,
      date.getDate() > 9 ? String(date.getDate()) : String(date.getDate()).padStart(2, "0")
    );
    str = str.replace(/d|D/g, `${date.getDate()}`);
    str = str.replace(
      /hh|HH/,
      date.getHours() > 9 ? String(date.getHours()) : String(date.getHours()).padStart(2, "0")
    );
    str = str.replace(/h|H/g, `${date.getHours()}`);
    str = str.replace(
      /mm/,
      date.getMinutes() > 9 ? String(date.getMinutes()) : String(date.getMinutes()).padStart(2, "0")
    );
    str = str.replace(/m/g, `${date.getMinutes()}`);
    str = str.replace(
      /ss|SS/,
      date.getSeconds() > 9 ? String(date.getSeconds()) : String(date.getSeconds()).padStart(2, "0")
    );
    str = str.replace(/s|S/g, `${date.getSeconds()}`);
    return str;
  } catch (error) {
    console.warn(error, date, formatStr);
  }
  return "";
}
function getSignature(arr, config) {
  let params = config.data || config.params;
  if (typeof params !== "string") {
    if (params) {
      params = JSON.stringify(params);
    } else {
      params = {};
    }
  }
  const CanonicalizedHeaders = `${arr.map((v) => {
    return `${v[0]}:${v[1]}`;
  }).join("\n")}
`;
  const urlConf = `/${config.url.replace(/^\//, "")}`;
  const StringToSign = `POST
${urlConf}
application/json
${CanonicalizedHeaders}`;
  const MD5SecretKey = md5(SecretKey, null).toLowerCase();
  const SigningKey = HmacSHA256(StringToSign, MD5SecretKey, true);
  return SigningKey;
}
function setSignatureHeader(config) {
  const xDate = formatDate(/* @__PURE__ */ new Date(), "YYYY-MM-DD hh:mm:ss");
  const xNonce = uuid();
  config.headers = { ...config.headers, "x-date": xDate, "x-nonce": xNonce };
  const arr = [
    ["x-date", xDate],
    ["x-nonce", xNonce],
    ["x-username", AKey]
    // ak
  ];
  config.headers["x-signature"] = getSignature(arr, config);
  config.headers["x-username"] = AKey;
  config.headers = {
    ...config.headers,
    "x-signature": getSignature(arr, config),
    "x-username": AKey
  };
}
class ZmqMsg {
  /**
   * 静态方法，用于创建请求
   * @param method 请求方法
   * @param data 请求数据
   * @returns 请求消息对象
   */
  static newRequest(method, data, id) {
    return {
      id: id || v4(),
      method,
      data: data || {}
    };
  }
  /**
   * 静态方法，用于创建响应
   * @param id 请求id
   * @param data 响应数据
   * @param status 响应状态
   * @returns 错误响应消息对象
   */
  static newResponse(data, status = "200", req) {
    return {
      id: req?.id || v4(),
      method: "__response__",
      data,
      status,
      success: status === "200",
      requestMethod: req?.method
    };
  }
  /**
   * 创建错误响应
   * @param id 请求id
   * @param error 错误信息
   * @returns 错误响应消息对象
   */
  static newErrorResponse(error, req) {
    return ZmqMsg.newResponse(error, "ERROR", req);
  }
  /**
   * 安全解析消息
   * @param input - 输入的消息字符串
   * @returns 解析后的消息对象
   */
  static safeParse(input) {
    try {
      const parsed = JSON.parse(input);
      if (parsed.status) {
        parsed.success = parsed.status === "200";
      }
      return parsed;
    } catch (e) {
      return {
        id: "parse-error",
        method: "__parse_error__",
        status: "ERROR",
        data: e?.message || e || "未知异常",
        success: false
      };
    }
  }
}
class ZmqUtils {
  /**
   * 发送zmq请求
   * @param method 请求方法
   * @param data 请求数据
   * @returns 请求结果
   */
  static async sendRequest(method, data) {
    try {
      const res = await window.api.zmqSendRequest(method, data);
      res.success = res.status == "200";
      if (res.success) {
        return res.data;
      } else {
        logger.warn("【zmq】请求结果失败：", res, method, data);
        return Promise.reject(res);
      }
    } catch (e) {
      return Promise.reject(ZmqMsg.newErrorResponse(e?.message || e, data));
    }
  }
  /**
   * 处理请求
   * @param method 请求方法
   * @param callback 请求响应处理方法
   */
  static registerHandler(method, callback) {
    return window.api.zmqRegisterHandler(method, callback);
  }
  static zmqCancelRegisterHandler(method, callback) {
    window.api.zmqCancelRegisterHandler(method, callback);
  }
  /**
   * 发送请求给cef
   * @param cefName 请求的cef
   * @param cefMethod 请求方法
   * @param cefData 请求数据
   * @returns 请求响应结果
   */
  static async sendRequestQtCef(cefName, cefMethod, cefData) {
    return await ZmqUtils.sendRequest(ZMQ_MESSAGE_TYPE.CALL_QT_CEF, {
      cefName,
      cefMethod,
      cefData: cefData || {}
    });
  }
  /**
   * 查询zmq连接状态
   * @param enpoint 连接终端地址
   * @return 是否连接中
   */
  static async queryConnectStatus(endpoint) {
    try {
      const res = await window.api.zmqQueryConnectStatus(endpoint);
      res.success = res.status == "200";
      if (res.success) {
        return res.data?.connected;
      } else {
        logger.warn("【zmq】查询zmq连接状态失败：", res, endpoint);
        return true;
      }
    } catch (e) {
      return Promise.reject(ZmqMsg.newErrorResponse(e?.message || e));
    }
  }
}
function showWarning(message, options = {}) {
  showToast(message, {
    ...options,
    type: "warning"
  });
}
async function showToast(message, options = {}) {
  try {
    const { duration = 3e3, type = "info", position = "bottom" } = options;
    await ZmqUtils.sendRequest("toast", {
      type,
      message,
      position,
      duration
    });
  } catch (e) {
    logger.error("【toast-zmq】", "弹框显示失败", e, message);
  }
}
const HTTP_STATUS_ERROR_MESSAGE_MAP = {
  400: "请求错误",
  401: "未授权，请重新登录",
  403: "拒绝访问",
  404: "请求出错",
  405: "请求方法不允许",
  408: "请求超时",
  500: "服务器错误",
  501: "服务器未实现",
  502: "网络错误",
  503: "服务不可用",
  504: "网络超时"
};
const axios = Axios.create({
  baseURL: "https://class.hailiangedu.com/zhhb",
  headers: {
    "Content-Type": "application/json",
    Pragma: "no-cache",
    "Cache-Control": "no-cache"
  },
  timeout: 30 * 1e3,
  timeoutErrorMessage: "请求超时，请重试~"
});
axios.interceptors.request.use(async (config) => {
  Object.assign(config.headers, {});
  Object.assign(config.headers, { endpoint: 1 });
  setSignatureHeader(config);
  return config;
});
axios.interceptors.response.use(
  async function(response) {
    const { data = {}, request: request2 } = response;
    console.log("【Axios】request", request2);
    if (data instanceof Blob) {
      return Promise.resolve(response);
    }
    if (data.status === RESPONSE_BUSINESS_STATUS_ENUM.SERVER_INFO) {
      return Promise.resolve(response);
    }
    if (data.status !== 200 || !data.success) {
      console.log("【Axios】data", data);
      data.message = data.message || "服务器未知异常";
      return Promise.reject(data);
    }
    return Promise.resolve(response);
  },
  function(error) {
    const defaultErrorResult = {
      data: null,
      status: RESPONSE_BUSINESS_STATUS_ENUM.SERVER_ERROR,
      message: "服务器未知异常",
      success: false,
      error
    };
    try {
      const { response } = error;
      const { status } = response || {};
      let errorMessage = defaultErrorResult.message;
      if (error.code === "ECONNABORTED" && error.message.indexOf("timeout") !== -1) {
        errorMessage = "网络超时，请稍后重试！";
      } else if (navigator.onLine === false) {
        errorMessage = "网络未连接，请联网后重试！";
      } else {
        errorMessage = HTTP_STATUS_ERROR_MESSAGE_MAP[status] || "服务器未知异常";
      }
      defaultErrorResult.message = errorMessage;
      return Promise.reject(defaultErrorResult);
    } catch (error2) {
      defaultErrorResult.error = error2;
      return Promise.reject(defaultErrorResult);
    }
  }
);
function request(options) {
  const { loading, closeErrorToast } = options || {};
  const notify = (message, type = "error") => {
    if (closeErrorToast) {
      return;
    }
    if (options?.isZmqQtShow) {
      showToast(message || "未知异常", {
        type
      });
    }
    if (options?.isBridgeQtShow) {
      showToast$1(message || "未知异常", {
        type
      });
    } else {
      showToast$2(message || "未知异常", {
        type
      });
    }
  };
  const promise = new Promise(function(resolve, reject) {
    axios(options).then(function(response) {
      const { data } = response;
      console.log("【Axios】response.data", data);
      if (data instanceof Blob) {
        return resolve(data);
      }
      if (data.status === RESPONSE_BUSINESS_STATUS_ENUM.SERVER_INFO && data.message) {
        notify(data.message, "info");
      }
      return resolve(data);
    }).catch(function(error) {
      switch (error?.status) {
        case RESPONSE_BUSINESS_STATUS_ENUM.BUSINESS_ERROR:
          notify(error?.message, "warning");
          logger.warn("【axios】业务异常:", error?.message);
          break;
        default:
          notify(error?.message);
          logger.warn("【axios】请求失败:", error);
      }
      reject(error);
    });
  });
  if (loading) {
    promise.finally(() => {
    });
  }
  return promise;
}
function $post(url, data, options) {
  return request({
    ...options,
    method: "POST",
    url,
    data
  });
}
export {
  $post as $,
  LoginType as L,
  Menu_ENUM as M,
  PhoneType as P,
  QRCodeType as Q,
  RESPONSE_BUSINESS_STATUS_ENUM as R,
  ZmqUtils as Z,
  ZmqMsg as a,
  showToast as b,
  showWarning as s
};
