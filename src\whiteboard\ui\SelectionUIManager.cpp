#include "SelectionUIManager.h"
#include "../core/WhiteBoardWidget.h"
#include "../graphics/DrawItem.h"
#include "../commands/CommandManager.h"
#include "../commands/GraphicsItemState.h"
#include "ShapeToolManager.h"
#include <QDebug>
#include <QSvgRenderer>
#include <QPainterPath>
#include <QGraphicsScene>
#include <QTimer>

SelectionUIManager::SelectionUIManager(QObject* parent)
    : QObject(parent)
    , m_view(nullptr)
    , m_isVisible(false)
    , m_interactionState(InteractionState::None)
    , m_activeAnchorType(AnchorType::TopLeft)
    , m_activeToolbarAction(ToolbarAction::Delete)
{
    // 初始化默认属性
    m_properties.selectionColor = RenderConstants::SELECTION_COLOR;
    m_properties.anchorColor = RenderConstants::ANCHOR_COLOR;
    m_properties.selectionBorderWidth = RenderConstants::SELECTION_BORDER_WIDTH();
    m_properties.anchorSize = RenderConstants::ANCHOR_SIZE();
    m_properties.anchorBorderWidth = RenderConstants::ANCHOR_BORDER_WIDTH();
    m_properties.showRotation = true;
    m_properties.showToolbar = true;

    initializeDefaultToolbarIcons();
}

SelectionUIManager::~SelectionUIManager()
{
}

void SelectionUIManager::setView(WhiteBoardWidget* view)
{
    if (m_view == view) return;

    // 断开旧视图的连接
    if (m_view) {
        disconnect(m_view, nullptr, this, nullptr);
    }

    m_view = view;

    // 连接新视图的信号
    if (m_view) {
        // 这里可以连接视图的更新信号，如果需要的话
        // connect(m_view, &WhiteBoardWidget::viewUpdated, this, &SelectionUIManager::onViewUpdated);
    }
}

void SelectionUIManager::showSelectionUI(const QList<QGraphicsItem*>& selectedItems)
{
    if (selectedItems.isEmpty()) {
        qDebug() << "showSelectionUI selectedItems is empty";
        hideSelectionUI();
        return;
    }

    m_selectedItems = selectedItems;
    m_isVisible = true;

    // 将选中的图形移动到活动层进行操作
    moveSelectedItemsToActiveLayer();

    // 计算边界矩形
    m_properties.boundingRect = calculateBoundingRect(selectedItems);

    // 确定锚点类型
    m_properties.anchorType = determineAnchorType(selectedItems);

    // 生成锚点
    m_anchors = generateAnchors(m_properties.anchorType, m_properties.boundingRect);

    // 是否显示旋转锚点
    m_properties.showRotation = determineShowRotation();
    // 计算旋转锚点位置
    if (m_properties.showRotation) {
        QPointF topCenter = QPointF(m_properties.boundingRect.center().x(), m_properties.boundingRect.top());
        m_rotationAnchorPos = QPointF(topCenter.x(), topCenter.y() - RenderConstants::ROTATION_DISTANCE());
    }

    // 计算工具栏区域
    if (m_properties.showToolbar) {
        qreal toolbarWidth = m_properties.toolbarIcons.size() * RenderConstants::TOOLBAR_ICON_SIZE() +
                           (m_properties.toolbarIcons.size() - 1) * RenderConstants::TOOLBAR_ICON_SPACING();
        QPointF toolbarCenter(m_properties.boundingRect.center().x(),
                             m_properties.boundingRect.bottom() + RenderConstants::TOOLBAR_DISTANCE() + RenderConstants::TOOLBAR_ICON_SIZE() / 2);
        m_toolbarRect = QRectF(toolbarCenter.x() - toolbarWidth / 2,
                              toolbarCenter.y() - RenderConstants::TOOLBAR_ICON_SIZE() / 2,
                              toolbarWidth, RenderConstants::TOOLBAR_ICON_SIZE());
    }

    emit selectionUIVisibilityChanged(true);

    if (m_view) {
        m_view->update();
    }
}

void SelectionUIManager::hideSelectionUI()
{
    if (!m_isVisible) return;

    // 将选中的图形移回历史层
    moveSelectedItemsToHistoryLayer();

    m_isVisible = false;
    m_selectedItems.clear();
    m_anchors.clear();

    emit selectionUIVisibilityChanged(false);

    if (m_view) {
        m_view->update();
    }
}

void SelectionUIManager::updateSelectionUI(const QList<QGraphicsItem*>& selectedItems)
{
    if (selectedItems.isEmpty()) {
        hideSelectionUI();
    } else {
        showSelectionUI(selectedItems);
    }
}

void SelectionUIManager::paintSelectionUI(QPainter* painter)
{
    if (!m_isVisible || !painter) return;

    // 如果正在旋转，不显示任何选择UI元素
    if (m_interactionState == InteractionState::Rotating) {
        return;
    }

    // 如果正在进行直线/箭头的端点拖拽，也不显示选择UI元素
    if (m_interactionState == InteractionState::Resizing &&
        m_properties.anchorType == SelectAnchorType::Line &&
        (m_activeAnchorType == AnchorType::StartPoint || m_activeAnchorType == AnchorType::EndPoint)) {
        return;
    }

    painter->save();
    painter->setRenderHint(QPainter::Antialiasing, true);

    // 绘制选择框边框
    drawSelectionBox(painter);

    // 绘制锚点
    drawAnchors(painter);

    // 绘制旋转元素
    if (m_properties.showRotation) {
        drawRotationElements(painter);
    }

    // 绘制工具栏
    if (m_properties.showToolbar) {
        drawToolbar(painter);
    }

    painter->restore();
}

QRectF SelectionUIManager::calculateBoundingRect(const QList<QGraphicsItem*>& items)
{
    if (items.isEmpty()) return QRectF();

    QRectF boundingRect;
    for (QGraphicsItem* item : items) {
        if (item) {
            QRectF itemRect = item->sceneBoundingRect();
            if (boundingRect.isNull()) {
                boundingRect = itemRect;
            } else {
                boundingRect = boundingRect.united(itemRect);
            }
        }
    }

    return boundingRect;
}

SelectAnchorType SelectionUIManager::determineAnchorType(const QList<QGraphicsItem*>& items)
{
    if (items.size() > 1) {
        return SelectAnchorType::FixedRatio;
    }

    if (items.size() == 1) {
        QGraphicsItem* item = items.first();

        // 检查是否是DrawItem
        if (item->type() == DrawItem::DrawItemType) {
            DrawItem *drawItem = qgraphicsitem_cast<DrawItem *>(item);
            if (drawItem) {
                ToolType toolType = drawItem->toolType();

                // 判断图形类型
                if (toolType == ToolType::Line || toolType == ToolType::DashedLine) {
                    // 直线：只显示起点和终点
                    return SelectAnchorType::Line;
                } else if (toolType == ToolType::Arrow) {
                    // 箭头：只显示起点和终点
                    return SelectAnchorType::Line;
                } else if (toolType == ToolType::Image) {
                    return SelectAnchorType::FixedRatio;
                }
                else if (isFixedRatioShape(drawItem)) {
                    return SelectAnchorType::FixedRatio;
                }
                else if (drawItem->has90Degrees()) {
                    return SelectAnchorType::Full;
                } else {
                    return SelectAnchorType::FixedRatio;
                }
            }
        }

        // 其他类型的图形：显示8个锚点
        return SelectAnchorType::Full;
    }

    return SelectAnchorType::Full;
}

QList<AnchorInfo> SelectionUIManager::generateAnchors(SelectAnchorType anchorType, const QRectF& boundingRect)
{
    QList<AnchorInfo> anchors;
    qreal hitArea = RenderConstants::ANCHOR_HIT_AREA(); // 使用更大的锚点命中区域

    switch (anchorType) {
    case SelectAnchorType::Line: {
        // 直线/箭头：只显示起点和终点
        // 对于直线和箭头，需要获取实际的起点和终点位置
        QPointF startPoint, endPoint;

        if (m_selectedItems.size() == 1) {
            QGraphicsItem* item = m_selectedItems.first();

            // 检查是否是DrawItem
            if (item->type() == DrawItem::DrawItemType) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem) {
                    ToolType toolType = drawItem->toolType();

                    if (toolType == ToolType::Line || toolType == ToolType::DashedLine || toolType == ToolType::Arrow) {
                        // 对于直线和箭头，使用边界矩形的对角点
                        if (drawItem->lineDirection() == DrawItem::LineDirection::DirectSlash) {
                            startPoint = drawItem->mapToScene(drawItem->lineStartPoint());
                            endPoint = drawItem->mapToScene(drawItem->lineEndPoint());
                        }
                        else {
                            startPoint = drawItem->mapToScene(drawItem->lineStartPoint());
                            endPoint = drawItem->mapToScene(drawItem->lineEndPoint());
                        }
                    }
                }
            }
        }

        // 如果没有获取到实际位置，使用边界矩形的对角点作为备用
        if (startPoint.isNull() || endPoint.isNull()) {
            startPoint = boundingRect.topLeft();
            endPoint = boundingRect.bottomRight();
        }

        AnchorInfo startAnchor;
        startAnchor.type = AnchorType::StartPoint;
        startAnchor.position = startPoint;
        qreal halfHitArea = hitArea / 2.0;
        startAnchor.hitRect = QRectF(startAnchor.position.x() - halfHitArea, startAnchor.position.y() - halfHitArea,
                                   hitArea, hitArea);
        anchors.append(startAnchor);

        AnchorInfo endAnchor;
        endAnchor.type = AnchorType::EndPoint;
        endAnchor.position = endPoint;
        endAnchor.hitRect = QRectF(endAnchor.position.x() - halfHitArea, endAnchor.position.y() - halfHitArea,
                                 hitArea, hitArea);
        anchors.append(endAnchor);
        break;
    }
    case SelectAnchorType::FixedRatio: {
        // 固定比例图形：显示4个角点
        QList<AnchorType> cornerTypes = {
            AnchorType::TopLeft, AnchorType::TopRight,
            AnchorType::BottomLeft, AnchorType::BottomRight
        };
        QList<QPointF> cornerPositions = {
            boundingRect.topLeft(), boundingRect.topRight(),
            boundingRect.bottomLeft(), boundingRect.bottomRight()
        };

        qreal halfHitArea = hitArea / 2.0;
        for (int i = 0; i < cornerTypes.size(); ++i) {
            AnchorInfo anchor;
            anchor.type = cornerTypes[i];
            anchor.position = cornerPositions[i];
            anchor.hitRect = QRectF(anchor.position.x() - halfHitArea, anchor.position.y() - halfHitArea,
                                  hitArea, hitArea);
            anchors.append(anchor);
        }
        break;
    }
    case SelectAnchorType::Full:
    case SelectAnchorType::Multiple: {
        // 完整锚点：显示8个锚点
        QList<AnchorType> allTypes = {
            AnchorType::TopLeft, AnchorType::TopCenter, AnchorType::TopRight,
            AnchorType::MiddleLeft, AnchorType::MiddleRight,
            AnchorType::BottomLeft, AnchorType::BottomCenter, AnchorType::BottomRight
        };
        QList<QPointF> allPositions = {
            boundingRect.topLeft(),
            QPointF(boundingRect.center().x(), boundingRect.top()),
            boundingRect.topRight(),
            QPointF(boundingRect.left(), boundingRect.center().y()),
            QPointF(boundingRect.right(), boundingRect.center().y()),
            boundingRect.bottomLeft(),
            QPointF(boundingRect.center().x(), boundingRect.bottom()),
            boundingRect.bottomRight()
        };

        qreal halfHitArea = hitArea / 2.0;
        for (int i = 0; i < allTypes.size(); ++i) {
            AnchorInfo anchor;
            anchor.type = allTypes[i];
            anchor.position = allPositions[i];
            anchor.hitRect = QRectF(anchor.position.x() - halfHitArea, anchor.position.y() - halfHitArea,
                                  hitArea, hitArea);
            anchors.append(anchor);
        }
        break;
    }
    }

    return anchors;
}

HitTestResult SelectionUIManager::hitTest(const QPointF& point, AnchorType& outAnchorType, ToolbarAction& outToolbarAction)
{
    if (!m_isVisible) {
        return HitTestResult::None;
    }

    if (m_properties.showToolbar && isPointInToolbar(point, outToolbarAction)) {
        return HitTestResult::Toolbar;
    }

    for (const AnchorInfo& anchor : m_anchors) {
        if (isPointInAnchor(point, anchor)) {
            outAnchorType = anchor.type;
            return HitTestResult::Anchor;
        }
    }

    // 检测旋转锚点（使用更大的命中区域）
    if (m_properties.showRotation) {
        qreal hitArea = RenderConstants::ANCHOR_HIT_AREA() * 1.5; // 旋转锚点使用1.5倍的命中区域
        qreal halfHitArea = hitArea / 2.0;
        QRectF rotationHitRect(m_rotationAnchorPos.x() - halfHitArea, m_rotationAnchorPos.y() - halfHitArea,
                              hitArea, hitArea);
        if (rotationHitRect.contains(point)) {
            outAnchorType = AnchorType::Rotation;
            return HitTestResult::Anchor;
        }
    }

    if (m_properties.boundingRect.contains(point)) {
        return HitTestResult::SelectionBox;
    }

    return HitTestResult::None;
}

void SelectionUIManager::setSelectionBoxProperties(const SelectionBoxProperties& properties)
{
    m_properties = properties;

    // 如果当前有选择UI显示，重新生成锚点
    if (m_isVisible && !m_selectedItems.isEmpty()) {
        m_anchors = generateAnchors(m_properties.anchorType, m_properties.boundingRect);

        if (m_view) {
            m_view->update();
        }
    }
}

void SelectionUIManager::onViewUpdated()
{
    // 视图更新时的处理逻辑
    if (m_isVisible && m_view) {
        // 可以在这里添加必要的更新逻辑
    }
}

void SelectionUIManager::drawSelectionBox(QPainter* painter)
{
    if (m_properties.anchorType == SelectAnchorType::Line) {
        // 直线类型不绘制矩形边框
        return;
    }

    QPen selectionPen(m_properties.selectionColor);
    selectionPen.setWidth(m_properties.selectionBorderWidth);
    selectionPen.setStyle(Qt::SolidLine);
    painter->setPen(selectionPen);
    painter->setBrush(Qt::NoBrush);

    painter->drawRect(m_properties.boundingRect);
}

void SelectionUIManager::drawAnchors(QPainter* painter)
{
    for (const AnchorInfo& anchor : m_anchors) {
        if (anchor.visible) {
            drawAnchor(painter, anchor);
        }
    }
}

void SelectionUIManager::drawRotationElements(QPainter* painter)
{
    // 绘制旋转锚点到顶部中心的虚线
    QPointF topCenter(m_properties.boundingRect.center().x(), m_properties.boundingRect.top());

    QPen dashPen(m_properties.selectionColor);
    dashPen.setWidth(m_properties.selectionBorderWidth);
    QVector<qreal> dashPattern;
    dashPattern << 1 << 2;
    dashPen.setDashPattern(dashPattern);
    painter->setPen(dashPen);

    // 计算虚线的起点和终点，避免覆盖锚点
    QPointF direction = m_rotationAnchorPos - topCenter;
    qreal lineLength = std::sqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (lineLength > 0) {
        // 计算需要避开的间隙：锚点半径 + 边框 + 额外间隙
        qreal anchorGap = m_properties.anchorSize / 2.0 + m_properties.anchorBorderWidth + 2.0;

        // 从顶部中心锚点边缘开始绘制虚线
        QPointF unitDirection = direction / lineLength;
        QPointF lineStartPoint = topCenter + unitDirection * anchorGap;

        // 到旋转锚点边缘结束绘制虚线
        qreal adjustedLength = lineLength - 2 * anchorGap;

        if (adjustedLength > 0) {
            QPointF lineEndPoint = lineStartPoint + unitDirection * adjustedLength;
            painter->drawLine(lineStartPoint, lineEndPoint);
        }
    }

    // 绘制旋转锚点
    AnchorInfo rotationAnchor;
    rotationAnchor.type = AnchorType::Rotation;
    rotationAnchor.position = m_rotationAnchorPos;
    rotationAnchor.visible = true;
    drawAnchor(painter, rotationAnchor);

    // 绘制旋转图标
    QPointF iconCenter(m_rotationAnchorPos.x(),
                      m_rotationAnchorPos.y() - RenderConstants::ROTATION_ICON_DISTANCE());
    QRectF iconRect(iconCenter.x() - RenderConstants::ROTATION_ICON_WIDTH() / 2.0,
                   iconCenter.y() - RenderConstants::ROTATION_ICON_HEIGHT() / 2.0,
                   RenderConstants::ROTATION_ICON_WIDTH(),
                   RenderConstants::ROTATION_ICON_HEIGHT());

    // 尝试加载旋转图标
    QSvgRenderer svgRenderer;
    if (svgRenderer.load(QString(":/whiteboard/images/select-rotate.svg"))) {
        svgRenderer.render(painter, iconRect);
    } else {
        // 备用绘制：简单的旋转符号
        painter->setPen(QPen(m_properties.selectionColor, 2));
        painter->setBrush(Qt::NoBrush);
        painter->drawEllipse(iconRect.adjusted(2, 2, -2, -2));

        // 绘制箭头
        QPointF arrowStart = iconRect.center() + QPointF(iconRect.width() / 4, 0);
        QPointF arrowEnd = arrowStart + QPointF(3, -3);
        painter->drawLine(arrowStart, arrowEnd);
        painter->drawLine(arrowStart, arrowStart + QPointF(3, 3));
    }
}

void SelectionUIManager::drawToolbar(QPainter* painter)
{
    if (m_properties.toolbarIcons.isEmpty()) return;

    QPointF toolbarCenter(m_properties.boundingRect.center().x(),
                         m_properties.boundingRect.bottom() + RenderConstants::TOOLBAR_DISTANCE() + RenderConstants::TOOLBAR_ICON_SIZE() / 2);

    qreal totalWidth = m_properties.toolbarIcons.size() * RenderConstants::TOOLBAR_ICON_SIZE() +
                      (m_properties.toolbarIcons.size() - 1) * RenderConstants::TOOLBAR_ICON_SPACING();
    qreal startX = toolbarCenter.x() - totalWidth / 2.0;

    for (int i = 0; i < m_properties.toolbarIcons.size(); ++i) {
        qreal iconX = startX + i * (RenderConstants::TOOLBAR_ICON_SIZE() + RenderConstants::TOOLBAR_ICON_SPACING());
        qreal iconY = toolbarCenter.y() - RenderConstants::TOOLBAR_ICON_SIZE() / 2.0;

        QRectF iconRect(iconX, iconY, RenderConstants::TOOLBAR_ICON_SIZE(), RenderConstants::TOOLBAR_ICON_SIZE());
        drawToolbarIcon(painter, iconRect, m_properties.toolbarIcons[i]);
    }
}

void SelectionUIManager::drawAnchor(QPainter* painter, const AnchorInfo& anchor)
{
    painter->save();

    // 绘制命中区域（调试用）
    if (m_properties.showHitAreas) {
        QPen hitAreaPen(QColor(255, 0, 0, 100)); // 半透明红色
        hitAreaPen.setWidth(1);
        painter->setPen(hitAreaPen);
        painter->setBrush(QBrush(QColor(255, 0, 0, 30))); // 半透明红色填充
        painter->drawRect(anchor.hitRect);
    }

    // 绘制锚点
    QPen anchorPen(m_properties.selectionColor);
    anchorPen.setWidth(m_properties.anchorBorderWidth);
    painter->setPen(anchorPen);
    painter->setBrush(m_properties.anchorColor);

    qreal halfSize = m_properties.anchorSize / 2.0;
    QRectF anchorRect(anchor.position.x() - halfSize, anchor.position.y() - halfSize,
                     m_properties.anchorSize, m_properties.anchorSize);

    painter->drawEllipse(anchorRect);

    painter->restore();
}

void SelectionUIManager::drawToolbarIcon(QPainter* painter, const QRectF& iconRect, const QString& iconPath)
{
    painter->save();
    painter->setRenderHint(QPainter::SmoothPixmapTransform, true);

    QSvgRenderer svgRenderer;
    if (svgRenderer.load(iconPath)) {
        svgRenderer.render(painter, iconRect);
    } else {
        // 备用绘制：简单的矩形和X
        painter->setPen(QPen(m_properties.selectionColor, 1));
        painter->setBrush(QBrush(m_properties.anchorColor));
        painter->drawRect(iconRect);

        painter->setPen(QPen(m_properties.selectionColor, 2));
        painter->drawLine(iconRect.topLeft(), iconRect.bottomRight());
        painter->drawLine(iconRect.topRight(), iconRect.bottomLeft());
    }

    painter->restore();
}

bool SelectionUIManager::isPointInAnchor(const QPointF& point, const AnchorInfo& anchor)
{
    // 检查点是否在锚点命中区域中，使用更大的命中区域 1.5倍
    QRectF hitRect = anchor.hitRect.adjusted(-RenderConstants::ANCHOR_HIT_AREA() * 0.5, -RenderConstants::ANCHOR_HIT_AREA() * 0.5,
                                             RenderConstants::ANCHOR_HIT_AREA() * 0.5, RenderConstants::ANCHOR_HIT_AREA() * 0.5);
    return hitRect.contains(point);
}

bool SelectionUIManager::isPointInToolbar(const QPointF& point, ToolbarAction& outAction)
{
    if (!m_toolbarRect.contains(point) || m_properties.toolbarIcons.isEmpty()) {
        return false;
    }

    qreal totalWidth = m_properties.toolbarIcons.size() * RenderConstants::TOOLBAR_ICON_SIZE() +
                      (m_properties.toolbarIcons.size() - 1) * RenderConstants::TOOLBAR_ICON_SPACING();
    qreal startX = m_toolbarRect.center().x() - totalWidth / 2.0;

    for (int i = 0; i < m_properties.toolbarIcons.size(); ++i) {
        qreal iconX = startX + i * (RenderConstants::TOOLBAR_ICON_SIZE() + RenderConstants::TOOLBAR_ICON_SPACING());
        QRectF iconRect(iconX, m_toolbarRect.y(), RenderConstants::TOOLBAR_ICON_SIZE(), RenderConstants::TOOLBAR_ICON_SIZE());

        if (iconRect.contains(point)) {
            switch (i) {
            case 0: outAction = ToolbarAction::Delete; break;
            case 1: outAction = ToolbarAction::Copy; break;
            case 2: outAction = ToolbarAction::Cut; break;
            case 3: outAction = ToolbarAction::BringToFront; break;
            case 4: outAction = ToolbarAction::SendToBack; break;
            default: outAction = ToolbarAction::Delete; break;
            }
            return true;
        }
    }

    return false;
}

void SelectionUIManager::initializeDefaultToolbarIcons()
{
    // 设置默认工具栏图标路径
    m_properties.toolbarIcons = {
        ":/whiteboard/images/select-tool-delete.svg"
        // 可以添加更多图标路径
        // ":/whiteboard/images/select-tool-copy.svg",
        // ":/whiteboard/images/select-tool-cut.svg",
        // ":/whiteboard/images/select-tool-front.svg",
        // ":/whiteboard/images/select-tool-back.svg"
    };
}

void SelectionUIManager::moveSelectedItemsToActiveLayer()
{
    if (!m_view) return;

    for (QGraphicsItem* item : m_selectedItems) {
        if (item) {
            item->setZValue(1000);
        }
    }
}

void SelectionUIManager::moveSelectedItemsToHistoryLayer()
{
    if (!m_view) return;

    for (QGraphicsItem* item : m_selectedItems) {
        if (item) {
            item->setZValue(0);
        }
    }
}

void SelectionUIManager::forceCleanupSelection()
{
    if (!m_isVisible && m_selectedItems.isEmpty()) {
        return;
    }

    if (m_interactionState != InteractionState::None) {
        cancelInteraction();
    }

    // 将选中的图形移回历史层
    moveSelectedItemsToHistoryLayer();

    // 清理状态
    m_isVisible = false;
    m_selectedItems.clear();
    m_anchors.clear();
    m_interactionState = InteractionState::None;

    // 重置交互相关状态
    m_interactionStartPoint = QPointF();
    m_lastInteractionPoint = QPointF();
    m_activeAnchorType = AnchorType::None;
    m_activeToolbarAction = ToolbarAction::None;

    emit selectionUIVisibilityChanged(false);

    if (m_view) {
        m_view->update();
    }
}

bool SelectionUIManager::shouldCleanupSelection(bool ignoreInteraction) const
{
    // 如果没有选择UI显示，不需要清理
    if (!m_isVisible && m_selectedItems.isEmpty()) {
        return false;
    }

    // 如果正在交互中且不忽略交互状态，不应该清理
    if (!ignoreInteraction && m_interactionState != InteractionState::None) {
        return false;
    }

    return true;
}

void SelectionUIManager::startInteraction(const QPointF& startPoint, HitTestResult hitType, AnchorType anchorType, ToolbarAction toolbarAction)
{
    if (m_interactionState != InteractionState::None) {
        return; // 已经在交互中
    }

    m_interactionStartPoint = startPoint;
    m_lastInteractionPoint = startPoint;
    m_activeAnchorType = anchorType;
    m_activeToolbarAction = toolbarAction;

    // 保存原始状态
    saveOriginalTransforms();

    switch (hitType) {
    case HitTestResult::Anchor:
        if (anchorType == AnchorType::Rotation) {
            m_interactionState = InteractionState::Rotating;
        } else {
            m_interactionState = InteractionState::Resizing;
        }
        break;

    case HitTestResult::SelectionBox:
        m_interactionState = InteractionState::Moving;
        break;

    case HitTestResult::Toolbar:
        m_interactionState = InteractionState::ToolbarAction;
        executeToolbarAction(toolbarAction);
        m_interactionState = InteractionState::None;
        m_activeToolbarAction = ToolbarAction::None;
        break;

    default:
        break;
    }
}

void SelectionUIManager::updateInteraction(const QPointF& currentPoint)
{
    if (m_interactionState == InteractionState::None) {
        return;
    }

    QPointF delta = currentPoint - m_lastInteractionPoint;
    QPointF totalDelta = currentPoint - m_interactionStartPoint;

    switch (m_interactionState) {
    case InteractionState::Moving:
        performMove(delta);
        // 移动操作已经在performMove中更新了选择框，不需要额外更新
        break;

    case InteractionState::Resizing:
        performResize(totalDelta);
        // 缩放操作需要重新计算选择框
        updateSelectionBoxDuringInteraction();
        break;

    case InteractionState::Rotating:
        performRotation(currentPoint);
        // 旋转操作不重新计算选择框，避免旋转中心漂移
        break;

    default:
        break;
    }

    m_lastInteractionPoint = currentPoint;

    // 更新选择框UI
    if (m_view) {
        m_view->update();
    }
}

void SelectionUIManager::finishInteraction(const QPointF& endPoint)
{
    Q_UNUSED(endPoint)

    if (m_interactionState == InteractionState::None) {
        return;
    }

    // 保存状态用于命令创建
    InteractionState oldState = m_interactionState;
    QMap<QGraphicsItem*, QTransform> originalTransforms = m_originalTransforms;
    QMap<QGraphicsItem*, QPointF> originalPositions = m_originalPositions;
    QMap<QGraphicsItem*, QJsonObject> originalItemData = m_originalJsons;

    // 清理状态
    m_interactionState = InteractionState::None;
    m_originalTransforms.clear();
    m_originalPositions.clear();
    m_originalJsons.clear();

    // 创建变换命令（如果有实际的变换发生）
    if (!originalTransforms.isEmpty() || !originalPositions.isEmpty()) {
        createTransformCommand(oldState, originalTransforms, originalPositions, originalItemData);

        // 立即更新选择框边界，避免闪烁
        // 由于命令是同步执行的，变换烘焙应该已经完成
        updateSelectionUIAfterTransform(oldState);
    } else {
        // 如果没有变换命令，直接更新选择框
        updateSelectionUIAfterTransform(oldState);
    }

    // 如果是缩放或旋转交互结束，触发相应方法以重置静态变量
    // 但跳过直线端点拖拽，因为它不需要重置静态变量
    if (oldState == InteractionState::Resizing &&
        !(m_properties.anchorType == SelectAnchorType::Line &&
          (m_activeAnchorType == AnchorType::StartPoint || m_activeAnchorType == AnchorType::EndPoint))) {
        performResize(QPointF(0, 0)); // 这将重置needInitialization标志
    } else if (oldState == InteractionState::Rotating) {
        performRotation(QPointF(0, 0)); // 这将重置needInitialization标志
    }
}

void SelectionUIManager::updateSelectionUIAfterTransform(InteractionState oldState)
{
    if (m_selectedItems.isEmpty()) {
        return;
    }

    // 如果是旋转操作结束，需要特殊处理DrawItem
    if (oldState == InteractionState::Rotating) {
        for (QGraphicsItem* item : m_selectedItems) {
            if (item && item->type() == DrawItem::DrawItemType) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem && isFreeDrawTool(drawItem->toolType())) {
                    drawItem->update();
                }
            }
        }
    }

    // 重新计算选择框边界（此时变换烘焙应该已经完成）
    m_properties.boundingRect = calculateBoundingRect(m_selectedItems);
    // 确定锚点类型
    m_properties.anchorType = determineAnchorType(m_selectedItems);
    m_anchors = generateAnchors(m_properties.anchorType, m_properties.boundingRect);

    // 重新计算旋转锚点位置
    if (m_properties.showRotation) {
        QPointF topCenter = QPointF(m_properties.boundingRect.center().x(), m_properties.boundingRect.top());
        m_rotationAnchorPos = QPointF(topCenter.x(), topCenter.y() - RenderConstants::ROTATION_DISTANCE());
    }

    // 重新计算工具栏区域
    if (m_properties.showToolbar && !m_properties.toolbarIcons.isEmpty()) {
        qreal toolbarWidth = m_properties.toolbarIcons.size() * RenderConstants::TOOLBAR_ICON_SIZE() +
                           (m_properties.toolbarIcons.size() - 1) * RenderConstants::TOOLBAR_ICON_SPACING();
        QPointF toolbarCenter(m_properties.boundingRect.center().x(),
                             m_properties.boundingRect.bottom() + RenderConstants::TOOLBAR_DISTANCE() + RenderConstants::TOOLBAR_ICON_SIZE() / 2);
        m_toolbarRect = QRectF(toolbarCenter.x() - toolbarWidth / 2,
                              toolbarCenter.y() - RenderConstants::TOOLBAR_ICON_SIZE() / 2,
                              toolbarWidth, RenderConstants::TOOLBAR_ICON_SIZE());
    }

    // 需要手动触发重绘以确保旋转锚点和工具栏区域被正确更新
    if (m_view) {
        // 计算需要更新的完整区域，包括旋转锚点和工具栏
        QRectF updateRect = m_properties.boundingRect;

        // 扩展更新区域以包含旋转锚点
        if (m_properties.showRotation) {
            qreal rotationIconSize = qMax(RenderConstants::ROTATION_ICON_WIDTH(), RenderConstants::ROTATION_ICON_HEIGHT());
            qreal rotationSpace = RenderConstants::ROTATION_DISTANCE() + RenderConstants::ROTATION_ICON_DISTANCE() + rotationIconSize;
            updateRect.adjust(0, -rotationSpace, 0, 0);
        }

        // 扩展更新区域以包含工具栏
        if (m_properties.showToolbar && !m_properties.toolbarIcons.isEmpty()) {
            qreal toolbarSpace = RenderConstants::TOOLBAR_DISTANCE() + RenderConstants::TOOLBAR_ICON_SIZE();
            updateRect.adjust(0, 0, 0, toolbarSpace);
        }

        // 添加一些边距以确保完整更新
        updateRect.adjust(-20, -20, 20, 20);

        m_view->update(updateRect.toRect());
    }
}

void SelectionUIManager::cancelInteraction()
{
    if (m_interactionState == InteractionState::None) {
        return;
    }

    // 恢复原始状态
    restoreOriginalTransforms();

    // 清理状态
    InteractionState oldState = m_interactionState;
    m_interactionState = InteractionState::None;
    m_originalTransforms.clear();
    m_originalPositions.clear();
    m_originalJsons.clear();

    // 如果是缩放或旋转交互取消，触发相应方法以重置静态变量
    if (oldState == InteractionState::Resizing) {
        performResize(QPointF(0, 0)); // 这将重置needInitialization标志
    } else if (oldState == InteractionState::Rotating) {
        performRotation(QPointF(0, 0)); // 这将重置needInitialization标志
    }

    if (m_view) {
        m_view->update();
    }
}

void SelectionUIManager::saveOriginalTransforms()
{
    m_originalTransforms.clear();
    m_originalPositions.clear();
    m_originalJsons.clear();

    for (QGraphicsItem* item : m_selectedItems) {
        if (item) {
            m_originalTransforms[item] = item->transform();
            m_originalPositions[item] = item->pos();
            if (item->type() == DrawItem::DrawItemType) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem) {
                    m_originalJsons[item] = drawItem->toJson();
                }
            }
        }
    }
}

void SelectionUIManager::restoreOriginalTransforms()
{
    for (QGraphicsItem* item : m_selectedItems) {
        if (item && m_originalTransforms.contains(item) && m_originalPositions.contains(item)) {
            item->setTransform(m_originalTransforms[item]);
            item->setPos(m_originalPositions[item]);
            if (item->type() == DrawItem::DrawItemType) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem) {
                    drawItem->fromJson(m_originalJsons[item]);
                }
            }
        }
    }
}

void SelectionUIManager::performMove(const QPointF& delta)
{
    for (QGraphicsItem* item : m_selectedItems) {
        if (item) {
            QPointF newPos = item->pos() + delta;
            item->setPos(newPos);
        }
    }

    // 对于移动操作，可以直接平移选择框边界，避免重新计算
    m_properties.boundingRect.translate(delta);

    // 同时平移旋转锚点位置
    if (m_properties.showRotation) {
        m_rotationAnchorPos += delta;
    }

    // 平移工具栏区域
    if (m_properties.showToolbar) {
        m_toolbarRect.translate(delta);
    }

    // 平移所有锚点位置
    for (AnchorInfo& anchor : m_anchors) {
        anchor.position += delta;
        anchor.hitRect.translate(delta);
    }
}

void SelectionUIManager::performResize(const QPointF& totalDelta)
{
    if (m_selectedItems.isEmpty()) return;

    // 如果是直线或箭头的端点拖拽，使用专门的处理方法
    if (m_properties.anchorType == SelectAnchorType::Line &&
        (m_activeAnchorType == AnchorType::StartPoint || m_activeAnchorType == AnchorType::EndPoint)) {
        performLineEndpointDrag(m_activeAnchorType, totalDelta);
        return;
    }

    // 使用实例变量而不是静态变量，避免状态混乱
    static QRectF savedOriginalBounds;
    static QPointF savedAnchorPoint;
    static QHash<QGraphicsItem*, QRectF> savedOriginalBounds_items;
    static bool needInitialization = true;

    // 检查是否需要重新初始化
    if (needInitialization || m_interactionState != InteractionState::Resizing) {
        savedOriginalBounds = m_properties.boundingRect;
        savedAnchorPoint = calculateScalingAnchorPoint(m_activeAnchorType, savedOriginalBounds);
        savedOriginalBounds_items.clear();

        // 保存每个图形项的原始边界
        for (QGraphicsItem* item : m_selectedItems) {
            if (item) {
                savedOriginalBounds_items[item] = item->sceneBoundingRect();
            }
        }

        needInitialization = false;
    }

    // 如果不是缩放交互，重置初始化标志并返回
    if (m_interactionState != InteractionState::Resizing) {
        needInitialization = true;
        return;
    }

    if (savedOriginalBounds.width() <= 0 || savedOriginalBounds.height() <= 0) return;

    // 计算缩放比例
    QPointF currentMousePoint = m_interactionStartPoint + totalDelta;
    QPointF initialVector = m_interactionStartPoint - savedAnchorPoint;
    QPointF currentVector = currentMousePoint - savedAnchorPoint;

    // 防止除零错误
    if (qAbs(initialVector.x()) < 0.001) initialVector.setX(0.001);
    if (qAbs(initialVector.y()) < 0.001) initialVector.setY(0.001);

    qreal scaleX = currentVector.x() / initialVector.x();
    qreal scaleY = currentVector.y() / initialVector.y();

    // 根据锚点类型调整缩放行为
    switch (m_activeAnchorType) {
    case AnchorType::TopCenter:
    case AnchorType::BottomCenter:
        scaleX = 1.0; // 仅垂直缩放
        break;
    case AnchorType::MiddleLeft:
    case AnchorType::MiddleRight:
        scaleY = 1.0; // 仅水平缩放
        break;
    default:
        break;
    }

    // 防止镜像缩放（负值），当越过固定锚点时应该缩小而不是反向放大
    const qreal minScale = 0.001; // 允许缩放到很小，接近一个点

    // 正确处理负缩放：当缩放为负时，意味着鼠标越过了固定锚点，应该给一个很小的正值
    if (scaleX < 0) {
        scaleX = minScale;
    } else {
        scaleX = qMax(minScale, scaleX);
    }

    if (scaleY < 0) {
        scaleY = minScale;
    } else {
        scaleY = qMax(minScale, scaleY);
    }

    // 对于固定比例的图形，保持宽高比
    if (m_properties.anchorType == SelectAnchorType::FixedRatio) {
        qreal avgScale = (scaleX + scaleY) / 2.0;
        scaleX = scaleY = qMax(minScale, avgScale); // 确保统一的正值缩放
    }

    // 应用缩放变换
    for (QGraphicsItem* item : m_selectedItems) {
        if (item && m_originalTransforms.contains(item) && m_originalPositions.contains(item)
            && savedOriginalBounds_items.contains(item)) {

            // 恢复到原始状态
            item->setTransform(m_originalTransforms[item]);
            item->setPos(m_originalPositions[item]);

            // 获取原始的中心点（基于保存的原始边界）
            QRectF originalItemBounds = savedOriginalBounds_items[item];
            QPointF originalCenter = originalItemBounds.center();

            // 计算相对于固定锚点的位置偏移
            QPointF offsetFromAnchor = originalCenter - savedAnchorPoint;

            // 应用缩放到偏移量
            QPointF scaledOffset = QPointF(offsetFromAnchor.x() * scaleX, offsetFromAnchor.y() * scaleY);

            // 计算新的中心位置
            QPointF newCenter = savedAnchorPoint + scaledOffset;

            // 计算位置调整量
            QPointF positionDelta = newCenter - originalCenter;

            // 应用缩放变换（围绕图形项自身的中心）
            QTransform scaleTransform = m_originalTransforms[item];
            QRectF itemBounds = item->boundingRect();
            QPointF itemCenter = itemBounds.center();

            scaleTransform.translate(itemCenter.x(), itemCenter.y());
            scaleTransform.scale(scaleX, scaleY);
            scaleTransform.translate(-itemCenter.x(), -itemCenter.y());

            // 应用变换和位置
            item->setTransform(scaleTransform);
            item->setPos(m_originalPositions[item] + positionDelta);
        }
    }

    if (m_interactionState != InteractionState::Resizing) {
        needInitialization = true;
    }
}

void SelectionUIManager::performRotation(const QPointF& currentPoint)
{
    if (m_selectedItems.isEmpty()) return;

    // 使用静态变量保存固定的旋转中心和原始项信息
    static QPointF savedRotationCenter;
    static QHash<QGraphicsItem*, QPointF> savedOriginalCenters;
    static bool needInitialization = true;

    // 检查是否需要重新初始化
    if (needInitialization || m_interactionState != InteractionState::Rotating) {
        savedRotationCenter = m_properties.boundingRect.center();
        savedOriginalCenters.clear();

        // 保存每个图形项的原始中心点（基于原始变换和位置）
        for (QGraphicsItem* item : m_selectedItems) {
            if (item && m_originalTransforms.contains(item) && m_originalPositions.contains(item)) {
                // 临时恢复原始状态来获取正确的中心点
                QTransform currentTransform = item->transform();
                QPointF currentPos = item->pos();

                item->setTransform(m_originalTransforms[item]);
                item->setPos(m_originalPositions[item]);
                QPointF originalCenter = item->sceneBoundingRect().center();
                savedOriginalCenters[item] = originalCenter;

                // 恢复当前状态
                item->setTransform(currentTransform);
                item->setPos(currentPos);
            }
        }

        needInitialization = false;
    }

    // 如果不是旋转交互，重置初始化标志并返回
    if (m_interactionState != InteractionState::Rotating) {
        needInitialization = true;
        return;
    }

    // 计算旋转角度（基于固定的旋转中心）
    QPointF startVector = m_interactionStartPoint - savedRotationCenter;
    QPointF currentVector = currentPoint - savedRotationCenter;

    qreal startAngle = std::atan2(startVector.y(), startVector.x());
    qreal currentAngle = std::atan2(currentVector.y(), currentVector.x());
    qreal deltaAngle = currentAngle - startAngle;

    // 转换为度数
    qreal deltaAngleDegrees = deltaAngle * 180.0 / M_PI;

    // 应用旋转到每个图形项
    for (QGraphicsItem* item : m_selectedItems) {
        if (item && m_originalTransforms.contains(item) && m_originalPositions.contains(item)
            && savedOriginalCenters.contains(item)) {

            // 恢复到原始状态
            item->setTransform(m_originalTransforms[item]);
            item->setPos(m_originalPositions[item]);

            // 获取原始中心点
            QPointF originalCenter = savedOriginalCenters[item];

            // 计算相对于旋转中心的偏移
            QPointF offsetFromRotationCenter = originalCenter - savedRotationCenter;

            // 旋转偏移向量
            QTransform rotationTransform;
            rotationTransform.rotate(deltaAngleDegrees);
            QPointF rotatedOffset = rotationTransform.map(offsetFromRotationCenter);

            // 计算新的中心位置
            QPointF newCenter = savedRotationCenter + rotatedOffset;

            // 计算位置调整量
            QPointF positionDelta = newCenter - originalCenter;

            // 应用旋转变换（围绕图形项自身的中心）
            QTransform itemTransform = m_originalTransforms[item];
            QRectF itemBounds = item->boundingRect();
            QPointF itemCenter = itemBounds.center();

            itemTransform.translate(itemCenter.x(), itemCenter.y());
            itemTransform.rotate(deltaAngleDegrees);
            itemTransform.translate(-itemCenter.x(), -itemCenter.y());

            // 应用变换和位置
            item->setTransform(itemTransform);
            item->setPos(m_originalPositions[item] + positionDelta);
        }
    }

    if (m_interactionState != InteractionState::Rotating) {
        needInitialization = true;
    }
}

void SelectionUIManager::performLineEndpointDrag(const AnchorType anchorType, const QPointF &totalDelta)
{
    if (m_selectedItems.isEmpty() || m_selectedItems.size() != 1) {
        return; // 只支持单个图形的端点拖拽
    }

    QGraphicsItem* item = m_selectedItems.first();
    if (!item) {
        return;
    }

    // 检查是否是DrawItem的直线或箭头类型
    if (item->type() != DrawItem::DrawItemType) {
        return;
    }

    DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
    if (!drawItem) {
        return;
    }

    ToolType toolType = drawItem->toolType();
    if (toolType != ToolType::Line && toolType != ToolType::DashedLine && toolType != ToolType::Arrow) {
        return;
    }

    // 在新架构中，端点拖拽功能暂时简化为整体移动
    if (anchorType == AnchorType::StartPoint) {
        // 移动起点
        QPointF newStartPoint = m_interactionStartPoint + totalDelta;
        QPointF itemPoint = drawItem->mapFromScene(newStartPoint);


        QPainterPath newPath = ShapeToolManager::instance()->getTool(toolType)->createPath(itemPoint, drawItem->lineEndPoint());
        drawItem->setPath(newPath);

    } else if (anchorType == AnchorType::EndPoint) {
        // 移动终点
        QPointF newEndPoint = m_interactionStartPoint + totalDelta;
        QPointF itemPoint = drawItem->mapFromScene(newEndPoint);

        QPainterPath newPath = ShapeToolManager::instance()->getTool(toolType)->createPath(drawItem->lineStartPoint(), itemPoint);
        drawItem->setPath(newPath);
    }
    updateSelectionBoxDuringInteraction();
}

void SelectionUIManager::executeToolbarAction(ToolbarAction action)
{
    switch (action) {
    case ToolbarAction::Delete:
        // 安全删除选中的图形
        deleteSelectedItemsSafely();
        break;

    case ToolbarAction::Copy:
        // TODO: 实现复制功能
        break;

    case ToolbarAction::Cut:
        // TODO: 实现剪切功能
        break;

    case ToolbarAction::BringToFront:
        if (m_view) {
            for (QGraphicsItem* item : m_selectedItems) {
                if (item) {
                    item->setZValue(2000);
                }
            }
        }
        break;

    case ToolbarAction::SendToBack:
        if (m_view) {
            for (QGraphicsItem* item : m_selectedItems) {
                if (item) {
                    item->setZValue(-1000);
                }
            }
        }
        break;

    default:
        break;
    }
}

void SelectionUIManager::updateSelectionBoxDuringInteraction()
{
    if (m_selectedItems.isEmpty()) {
        return;
    }

    // 重新计算选择框边界
    m_properties.boundingRect = calculateBoundingRect(m_selectedItems);

    // 重新生成锚点（基于新的边界）
    m_anchors = generateAnchors(m_properties.anchorType, m_properties.boundingRect);

    // 重新计算旋转锚点位置
    if (m_properties.showRotation) {
        QPointF topCenter = QPointF(m_properties.boundingRect.center().x(), m_properties.boundingRect.top());
        m_rotationAnchorPos = QPointF(topCenter.x(), topCenter.y() - RenderConstants::ROTATION_DISTANCE());
    }

    // 重新计算工具栏区域
    if (m_properties.showToolbar && !m_properties.toolbarIcons.isEmpty()) {
        qreal toolbarWidth = m_properties.toolbarIcons.size() * RenderConstants::TOOLBAR_ICON_SIZE() +
                           (m_properties.toolbarIcons.size() - 1) * RenderConstants::TOOLBAR_ICON_SPACING();
        QPointF toolbarCenter(m_properties.boundingRect.center().x(),
                             m_properties.boundingRect.bottom() + RenderConstants::TOOLBAR_DISTANCE() + RenderConstants::TOOLBAR_ICON_SIZE() / 2);
        m_toolbarRect = QRectF(toolbarCenter.x() - toolbarWidth / 2,
                              toolbarCenter.y() - RenderConstants::TOOLBAR_ICON_SIZE() / 2,
                              toolbarWidth, RenderConstants::TOOLBAR_ICON_SIZE());
    }
}

QPointF SelectionUIManager::calculateScalingAnchorPoint(AnchorType anchorType, const QRectF& bounds) const
{
    // 根据当前锚点类型返回相对的锚点位置（对角锚点）
    switch (anchorType) {
    case AnchorType::TopLeft:
        return bounds.bottomRight();

    case AnchorType::TopRight:
        return bounds.bottomLeft();

    case AnchorType::BottomLeft:
        return bounds.topRight();

    case AnchorType::BottomRight:
        return bounds.topLeft();

    case AnchorType::TopCenter:
        return QPointF(bounds.center().x(), bounds.bottom());

    case AnchorType::BottomCenter:
        return QPointF(bounds.center().x(), bounds.top());

    case AnchorType::MiddleLeft:
        return QPointF(bounds.right(), bounds.center().y());

    case AnchorType::MiddleRight:
        return QPointF(bounds.left(), bounds.center().y());

    default:
        return bounds.center();
    }
}

QPointF SelectionUIManager::getOriginalDragPoint(AnchorType anchorType, const QRectF& bounds) const
{
    // 根据锚点类型返回对应的拖拽点位置
    switch (anchorType) {
    case AnchorType::TopLeft:
        return bounds.topLeft();

    case AnchorType::TopRight:
        return bounds.topRight();

    case AnchorType::BottomLeft:
        return bounds.bottomLeft();

    case AnchorType::BottomRight:
        return bounds.bottomRight();

    case AnchorType::TopCenter:
        return QPointF(bounds.center().x(), bounds.top());

    case AnchorType::BottomCenter:
        return QPointF(bounds.center().x(), bounds.bottom());

    case AnchorType::MiddleLeft:
        return QPointF(bounds.left(), bounds.center().y());

    case AnchorType::MiddleRight:
        return QPointF(bounds.right(), bounds.center().y());

    default:
        return bounds.center();
    }
}

void SelectionUIManager::resetResizeStaticVariables()
{
    // 这个函数用于重置 performResize 中的静态变量
    // 我们通过再次调用 performResize 并设置一个特殊标志来实现
    // 但更简单的方法是直接在 performResize 中检查交互状态
}

void SelectionUIManager::deleteSelectedItemsSafely()
{
    if (m_selectedItems.isEmpty()) {
        return;
    }

    // 创建待删除项的副本，避免在删除过程中修改原列表
    QList<QGraphicsItem*> itemsToDelete = m_selectedItems;

    // 先清空选择状态和隐藏UI
    m_selectedItems.clear();
    m_isVisible = false;
    m_anchors.clear();
    emit selectionUIVisibilityChanged(false);

    if (!m_view || !m_view->scene()) {
        return;
    }

    // 使用统一命令系统删除图形项，支持撤销/重做
    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        cmdManager->deleteItems(itemsToDelete);
    } else {
        performDirectDeletion(itemsToDelete);
    }

    if (m_view) {
        m_view->update();
    }
}

// 直接删除图形项（回退方案）
void SelectionUIManager::performDirectDeletion(const QList<QGraphicsItem*>& itemsToDelete)
{
    if (!m_view || !m_view->scene()) {
        return;
    }

    QGraphicsScene* scene = m_view->scene();

    // 暂时禁用BSP树索引以避免崩溃
    QGraphicsScene::ItemIndexMethod oldIndexMethod = scene->itemIndexMethod();
    scene->setItemIndexMethod(QGraphicsScene::NoIndex);

    QList<QGraphicsItem*> removedItems;
    for (QGraphicsItem* item : itemsToDelete) {
        if (item) {
            if (item->scene() == scene) {
                scene->removeItem(item);
                removedItems.append(item);
            }
        }
    }

    // 强制场景更新，清理内部索引
    scene->update();

    // 恢复索引方法
    scene->setItemIndexMethod(oldIndexMethod);

    // 使用定时器延迟删除对象，确保所有绘制操作完成
    QTimer::singleShot(0, [removedItems]() {
        for (QGraphicsItem* item : removedItems) {
            if (item) {
                delete item;
            }
        }
    });
}

// 创建变换命令
void SelectionUIManager::createTransformCommand(InteractionState interactionType,
                                                const QMap<QGraphicsItem *, QTransform> &originalTransforms,
                                                const QMap<QGraphicsItem *, QPointF> &originalPositions,
                                                const QMap<QGraphicsItem *, QJsonObject> &originalItemDatas)
{
    if (m_selectedItems.isEmpty()) {
        return;
    }

    // 检查是否有实际的变换发生
    bool hasTransformChanged = false;
    for (QGraphicsItem* item : m_selectedItems) {
        if (!item) continue;

        // 检查变换矩阵是否改变
        if (originalTransforms.contains(item)) {
            QTransform currentTransform = item->transform();
            QTransform originalTransform = originalTransforms[item];
            if (currentTransform != originalTransform) {
                hasTransformChanged = true;
                break;
            }
        }

        // 检查位置是否改变
        if (originalPositions.contains(item)) {
            QPointF currentPos = item->pos();
            QPointF originalPos = originalPositions[item];
            if (currentPos != originalPos) {
                hasTransformChanged = true;
                break;
            }
        }

        // 检查itemData是否改变
        if (item->type() == DrawItem::DrawItemType) {
            DrawItem *drawItem = static_cast<DrawItem *>(item);
            QJsonObject currentItemData = drawItem->toJson();
            QJsonObject originalItemData = originalItemDatas[drawItem];
            if (currentItemData != originalItemData) {
                hasTransformChanged = true;
                break;
            }
        }

    }

    if (!hasTransformChanged) {
        return;
    }

    // 确定变换类型
    QString transformType;
    switch (interactionType) {
    case InteractionState::Moving:
        transformType = "移动";
        break;
    case InteractionState::Resizing:
        transformType = "缩放";
        break;
    case InteractionState::Rotating:
        transformType = "旋转";
        break;
    default:
        transformType = "变换";
        break;
    }

    // 使用统一命令系统创建变换命令
    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        // 创建变换前后的状态
        QList<GraphicsItemState> beforeStates;
        QList<GraphicsItemState> afterStates;

        for (QGraphicsItem* item : m_selectedItems) {
            if (!item) continue;

            // 创建变换前状态
            GraphicsItemState beforeState = GraphicsItemState::fromGraphicsItem(item);
            QTransform originalTransform = originalTransforms.value(item, item->transform());
            QPointF originalPosition = originalPositions.value(item, item->pos());
            beforeState.transform = originalTransform;
            beforeState.position = originalPosition;
            beforeState.itemData = originalItemDatas.value(dynamic_cast<DrawItem *>(item), QJsonObject());
            beforeStates.append(beforeState);

            // 创建变换后状态
            GraphicsItemState afterState = GraphicsItemState::fromGraphicsItem(item);
            afterStates.append(afterState);
        }

        QString description = QString("%1图形").arg(transformType);
        cmdManager->transformItems(m_selectedItems, beforeStates, afterStates, description);
    }
}

// 辅助方法：判断是否是固定比例图形
bool SelectionUIManager::isFixedRatioShape(DrawItem* item) const
{
    if (!item) return false;

    ToolType toolType = item->toolType();
    return toolType == ToolType::Circle || toolType == ToolType::Square;
}

void SelectionUIManager::setShowHitAreas(bool show)
{
    if (m_properties.showHitAreas != show) {
        m_properties.showHitAreas = show;

        // 如果选择UI当前可见，触发重绘
        if (m_isVisible && m_view) {
            m_view->update();
        }

        qDebug() << "SelectionUIManager: 命中区域显示" << (show ? "开启" : "关闭");
    }
}

bool SelectionUIManager::determineShowRotation() {
    // 线段类不显示旋转
    if (m_properties.anchorType == SelectAnchorType::Line) {
        return false;
    }
    return true;
}


