#include "WhiteBoard.h"
#include "WhiteBoardWidget.h"
#include "WhiteBoardScene.h"
#include "../commands/CommandManager.h"
#include "../serialization/SceneSerializer.h"
#include "../export/ImageExporter.h"
#include "../graphics/DrawItem.h"
#include "../constants/DrawingConstants.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QApplication>
#include <QScreen>
#include <QThread>

// 简单的屏幕适配函数
namespace {
    qreal getScreenScaleFactor() {
        static qreal scaleFactor = 1.0;
        static bool calculated = false;

        if (!calculated) {
            QScreen* screen = QApplication::primaryScreen();
            if (screen) {
                // 基于4K基准计算缩放因子
                const qreal baseWidth = 4096.0;
                const qreal baseHeight = 2160.0;

                QSize screenSize = screen->size();
                qreal widthRatio = screenSize.width() / baseWidth;
                qreal heightRatio = screenSize.height() / baseHeight;

                // 使用较小的比例确保不超出屏幕
                scaleFactor = qMin(widthRatio, heightRatio);

                // 限制缩放范围
                scaleFactor = qBound(0.3, scaleFactor, 3.0);
            }
            calculated = true;
        }

        return scaleFactor;
    }

    qreal adaptSize(qreal originalSize) {
        return originalSize * getScreenScaleFactor();
    }
}

WhiteBoard::WhiteBoard(QObject* parent)
    : QObject(parent)
    , m_currentTool(ToolType::FreeDraw)
    , m_currentColor(Qt::black)
    , m_currentLineWidth(2.0)
    , m_currentPenStyle(Qt::SolidLine)
    , m_currentPenType(PenType::Solid)
    , m_currentOpacity(1.0)
    , m_currentFillColor(Qt::transparent)
    , m_currentFilled(false)
    , m_dpiScale(1.0)
    , m_defaultImageWidth(adaptSize(400.0))
    , m_defaultImageMaxHeight(adaptSize(800.0))
    , m_initialized(false)
{
    m_sceneSerializer.reset(new SceneSerializer(this));
    m_imageExporter.reset(new ImageExporter(this));
    m_view = new WhiteBoardWidget(qobject_cast<QWidget*>(parent));
}

WhiteBoard::~WhiteBoard()
{
    disconnect(this, nullptr, nullptr, nullptr);
    setProperty("destroying", true);
}

void WhiteBoard::initialize()
{
    if (m_initialized) return;

    WhiteBoardScene* scene = new WhiteBoardScene(this);
    m_scene = scene;

    m_view->setWhiteBoardScene(m_scene.data());

    m_scene->initialize();
    setupConnections();

    CommandManager* commandManager = CommandManager::instance();
    if (commandManager && m_scene) {
        commandManager->initialize(m_scene.data());
    }

    if (m_sceneSerializer) {
        m_sceneSerializer->setScene(m_scene.data());
    }

    if (m_imageExporter) {
        m_imageExporter->setScene(m_scene.data());
        m_imageExporter->setView(m_view);
    }

    setCurrentTool(ToolType::Lasso);  // 默认使用套索选择工具
    // 绘制性能检测
    enableDrawingPerformanceProfiler(false);
    m_initialized = true;
}

WhiteBoardWidget* WhiteBoard::getView() const
{
    return m_view;
}

WhiteBoardScene* WhiteBoard::getScene() const
{
    return m_scene.data();
}

void WhiteBoard::setCurrentTool(ToolType toolType)
{
    m_currentTool = toolType;

    // 根据工具类型自动设置画笔类型
    if (toolType == ToolType::FreeDraw) {
        m_currentPenType = PenType::Solid;
    } else if (toolType == ToolType::FreeDrawDashed) {
        m_currentPenType = PenType::Dashed;
    } else if (toolType == ToolType::FreeDrawHighlighter) {
        m_currentPenType = PenType::Highlighter;
    } else {
        m_currentPenType = PenType::Solid;
    }

    // 更新画笔样式
    updatePenFromType();

    if (m_view) {
        WhiteBoardWidget::ToolType viewToolType = static_cast<WhiteBoardWidget::ToolType>(toolType);
        m_view->setCurrentTool(viewToolType);
    }
    emit toolChanged(m_currentTool, toolType);
}

WhiteBoard::ToolType WhiteBoard::getCurrentTool() const
{
    return m_currentTool;
}

void WhiteBoard::cancelCurrentOperation()
{
    if (m_view) {
        m_view->cancelAllDrawing();
    }
}

void WhiteBoard::finishCurrentOperation()
{
}

void WhiteBoard::setDrawingColor(const QColor& color)
{
    m_currentColor = color;
    updatePenFromType();
    emit colorChanged(color);
}

void WhiteBoard::setLineWidth(qreal width)
{
    m_currentLineWidth = width;
    updatePenFromType();
    emit lineWidthChanged(width);
}

void WhiteBoard::setPenStyle(Qt::PenStyle style)
{
    m_currentPenStyle = style;
    if (m_view) {
        QPen pen(m_currentColor, m_currentLineWidth, style, Qt::RoundCap, Qt::RoundJoin);
        m_view->setPen(pen);
    }
}

void WhiteBoard::setPenType(PenType type)
{
    m_currentPenType = type;
    updatePenFromType();
}

void WhiteBoard::setOpacity(qreal opacity)
{
    m_currentOpacity = opacity;
    updatePenFromType();
    emit opacityChanged(opacity);
}

void WhiteBoard::setFillColor(const QColor& color)
{
    m_currentFillColor = color;
    if (m_view) {
        QBrush brush(color);
        m_view->setBrush(brush);
    }
}

void WhiteBoard::setFilled(bool filled)
{
    m_currentFilled = filled;
    if (m_view) {
        QBrush brush = filled ? QBrush(m_currentFillColor) : QBrush(Qt::NoBrush);
        m_view->setBrush(brush);
    }
}

void WhiteBoard::setEraserSize(const QSizeF& size)
{
    if (m_view) {
        m_view->setEraserSize(size);
    }
}

QColor WhiteBoard::getDrawingColor() const
{
    return m_currentColor;
}

qreal WhiteBoard::getLineWidth() const
{
    return m_currentLineWidth;
}

Qt::PenStyle WhiteBoard::getPenStyle() const
{
    return m_currentPenStyle;
}

PenType WhiteBoard::getPenType() const
{
    return m_currentPenType;
}

qreal WhiteBoard::getOpacity() const
{
    return m_currentOpacity;
}

QColor WhiteBoard::getFillColor() const
{
    return m_currentFillColor;
}

bool WhiteBoard::isFilled() const
{
    return m_currentFilled;
}

void WhiteBoard::clearScene()
{
    if (!m_scene) {
        return;
    }

    emit aboutToClear();

    // 使用统一命令系统清空场景，支持撤销/重做
    CommandManager* cmdManager = getCommandManager();
    if (cmdManager && cmdManager->isInitialized()) {
        cmdManager->clearScene();
    } else {
        // 回退到直接清空
        m_scene->clearAllItems();
    }

    emit sceneCleared();
    emit contentChanged();
}

void WhiteBoard::clearAll()
{
    emit aboutToClear();

    if (m_view) {
        m_view->cancelAllDrawing();
        m_view->clearHistoryCache();
    }

    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        cmdManager->clear();
    }

    if (m_scene) {
        m_scene->clear();
        m_scene->clearAllItems();
    }

    emit allCleared();
    emit contentChanged();
}

void WhiteBoard::clearHistory()
{
    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        cmdManager->clear();
    }

    emit historyCleared();
    emit contentChanged();
}

void WhiteBoard::clearWithConfirmation()
{
    if (hasContent()) {
        emit aboutToClear();
    }
}

void WhiteBoard::moveAllToHistory()
{
    if (m_view) {
        m_view->cancelAllDrawing();
    }
}

int WhiteBoard::getActiveItemCount() const
{
    return 0;
}

int WhiteBoard::getHistoryItemCount() const
{
    if (m_scene) {
        return m_scene->getTotalItemCount();
    }
    return 0;
}

bool WhiteBoard::hasContent() const
{
    bool hasGraphics = (getHistoryItemCount() > 0) || (getActiveItemCount() > 0);

    bool hasCommands = false;
    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        hasCommands = cmdManager->canUndo();
    }

    return hasGraphics || hasCommands;
}

bool WhiteBoard::canClear() const
{
    return hasContent();
}



void WhiteBoard::updateDPIScale(qreal scale)
{
    m_dpiScale = scale;
}

qreal WhiteBoard::getDPIScale() const
{
    return m_dpiScale;
}

void WhiteBoard::setSceneRect(const QRectF& rect)
{
    if (m_scene) {
        m_scene->setSceneRect(rect);
    }
}

QRectF WhiteBoard::getSceneRect() const
{
    if (m_scene) {
        return m_scene->sceneRect();
    }
    return QRectF();
}



void WhiteBoard::onToolChanged(ToolType tool)
{
    m_currentTool = tool;
    emit toolChanged(m_currentTool, tool);
}

void WhiteBoard::onDrawingStarted(int touchId)
{
    emit drawingStarted(m_currentTool);
}

void WhiteBoard::onDrawingFinished(int touchId)
{
    emit drawingFinished(m_currentTool);
}

void WhiteBoard::onSceneChanged()
{
    emit sceneChanged();
    emit itemCountChanged(getActiveItemCount(), getHistoryItemCount());
}

void WhiteBoard::setupConnections()
{
    if (m_view) {
        connect(m_view, &WhiteBoardWidget::toolChanged, this, &WhiteBoard::onToolChanged);
        connect(m_view, &WhiteBoardWidget::drawingStarted, this, &WhiteBoard::onDrawingStarted);
        connect(m_view, &WhiteBoardWidget::drawingFinished, this, &WhiteBoard::onDrawingFinished);
    }

    if (m_scene) {
        connect(m_scene.data(), &WhiteBoardScene::sceneChanged, this, &WhiteBoard::onSceneChanged);
    }

    CommandManager* commandManager = CommandManager::instance();
    if (commandManager) {
        connect(commandManager, &CommandManager::canUndoChanged, this, &WhiteBoard::canUndoChanged);
        connect(commandManager, &CommandManager::canRedoChanged, this, &WhiteBoard::canRedoChanged);
        connect(commandManager, &CommandManager::commandExecuted, this, &WhiteBoard::commandExecuted);
        connect(commandManager, &CommandManager::operationChanged, this, &WhiteBoard::operationChanged);
    }
}

bool WhiteBoard::canUndo() const
{
    return m_view ? m_view->canUndo() : false;
}

bool WhiteBoard::canRedo() const
{
    return m_view ? m_view->canRedo() : false;
}

bool WhiteBoard::undo()
{
    if (m_view) {
        return m_view->undo();
    }
    return false;
}

bool WhiteBoard::redo()
{
    if (m_view) {
        return m_view->redo();
    }
    return false;
}

void WhiteBoard::clearCommandHistory()
{
    CommandManager* commandManager = CommandManager::instance();
    if (commandManager && commandManager->isInitialized()) {
        commandManager->clear();
    }
}

CommandManager* WhiteBoard::getCommandManager() const
{
    CommandManager* commandManager = CommandManager::instance();
    return commandManager && commandManager->isInitialized() ? commandManager : nullptr;
}

void WhiteBoard::setMultiTouchEnabled(bool enabled)
{
    if (m_view) {
        m_view->setMultiTouchEnabled(enabled);
    }
}

bool WhiteBoard::isMultiTouchEnabled() const
{
    return m_view ? m_view->isMultiTouchEnabled() : false;
}

int WhiteBoard::getMaxTouchPoints() const
{
    return m_view ? m_view->getMaxTouchPoints() : 0;
}

bool WhiteBoard::saveToFile(const QString& filePath, const QVariantMap& metadata)
{
    if (!m_sceneSerializer) {
        qWarning() << "SceneSerializer not initialized";
        return false;
    }

    return m_sceneSerializer->saveScene(filePath, metadata);
}

bool WhiteBoard::loadFromFile(const QString& filePath, QVariantMap* metadata)
{
    if (!m_sceneSerializer) {
        qWarning() << "SceneSerializer not initialized";
        return false;
    }

    return m_sceneSerializer->loadScene(filePath, metadata);
}

QJsonObject WhiteBoard::exportToJson(const QVariantMap& metadata, bool includeCommandStack)
{
    if (!m_sceneSerializer) {
        qWarning() << "SceneSerializer not initialized";
        return QJsonObject();
    }

    // 获取场景数据
    QJsonObject sceneJson = m_sceneSerializer->serializeScene(metadata);

    // 如果需要包含操作栈
    if (includeCommandStack) {
        CommandManager* cmdManager = getCommandManager();
        if (cmdManager && cmdManager->isInitialized()) {
            QJsonObject commandStackJson = cmdManager->exportCommandStack(true);
            if (!commandStackJson.isEmpty()) {
                sceneJson["commandStack"] = commandStackJson;
                sceneJson["hasCommandStack"] = true;
            }
        }
    }

    return sceneJson;
}

bool WhiteBoard::importFromJson(const QJsonObject& jsonData, QVariantMap* metadata, bool mergeMode)
{
    if (!m_sceneSerializer) {
        qWarning() << "SceneSerializer not initialized";
        return false;
    }

    if (QThread::currentThread() != qApp->thread()) {
        qWarning() << "importFromJson not called from main thread";
        return false;
    }

    // 先导入场景数据
    bool sceneSuccess = m_sceneSerializer->deserializeScene(jsonData, metadata);

    // 如果包含操作栈数据，则导入操作栈
    if (sceneSuccess && jsonData.contains("commandStack") && jsonData["hasCommandStack"].toBool()) {
        QJsonObject commandStackJson = jsonData["commandStack"].toObject();

        CommandManager* cmdManager = getCommandManager();
        if (cmdManager && cmdManager->isInitialized()) {
            bool stackSuccess = cmdManager->importCommandStack(commandStackJson, mergeMode);
            if (stackSuccess) {
                qDebug() << "[WHITEBOARD] Successfully imported scene with command stack";
            } else {
                qWarning() << "[WHITEBOARD] Scene imported but command stack import failed";
            }
        } else {
            qWarning() << "[WHITEBOARD] Scene imported but CommandManager not available for stack import";
        }
    }

    return sceneSuccess;
}

QString WhiteBoard::exportToJsonString(bool prettyFormat)
{
    QJsonObject jsonObj = exportToJson();
    if (jsonObj.isEmpty()) {
        return QString();
    }

    QJsonDocument doc(jsonObj);
    return prettyFormat ? doc.toJson(QJsonDocument::Indented) : doc.toJson(QJsonDocument::Compact);
}

bool WhiteBoard::exportToImage(const QString& filePath, const QString& format,
                              const QSize& size, const QVariantMap& options)
{
    if (!m_imageExporter) {
        qWarning() << "ImageExporter not initialized";
        return false;
    }

    // 转换格式字符串到枚举
    ImageExporter::ImageFormat imageFormat = ImageExporter::PNG;
    QString formatLower = format.toLower();
    if (formatLower == "png") imageFormat = ImageExporter::PNG;
    else if (formatLower == "jpg" || formatLower == "jpeg") imageFormat = ImageExporter::JPG;
    else if (formatLower == "bmp") imageFormat = ImageExporter::BMP;
    else if (formatLower == "tiff" || formatLower == "tif") imageFormat = ImageExporter::TIFF;
    else if (formatLower == "svg") imageFormat = ImageExporter::SVG;
    else if (formatLower == "pdf") imageFormat = ImageExporter::PDF;

    // 构建导出选项
    ImageExporter::ExportOptions exportOptions;
    if (!size.isEmpty()) {
        exportOptions.imageSize = size;
    }

    // 从QVariantMap设置选项
    if (options.contains("quality")) {
        exportOptions.quality = static_cast<ImageExporter::Quality>(options["quality"].toInt());
    }
    if (options.contains("dpi")) {
        exportOptions.dpi = options["dpi"].toInt();
    }
    if (options.contains("backgroundColor")) {
        exportOptions.customBackgroundColor = options["backgroundColor"].value<QColor>();
        exportOptions.backgroundMode = ImageExporter::Custom;
    }
    if (options.contains("transparent")) {
        exportOptions.backgroundMode = options["transparent"].toBool() ?
                                      ImageExporter::Transparent : ImageExporter::White;
    }
    if (options.contains("antialiasing")) {
        exportOptions.antialiasing = options["antialiasing"].toBool();
    }

    return m_imageExporter->exportToFile(filePath, imageFormat, exportOptions);
}

QPixmap WhiteBoard::exportToPixmap(const QSize& size, const QVariantMap& options)
{
    if (!m_imageExporter) {
        qWarning() << "ImageExporter not initialized";
        return QPixmap();
    }

    ImageExporter::ExportOptions exportOptions;
    if (!size.isEmpty()) {
        exportOptions.imageSize = size;
    }

    // 从QVariantMap设置选项
    if (options.contains("backgroundColor")) {
        exportOptions.customBackgroundColor = options["backgroundColor"].value<QColor>();
        exportOptions.backgroundMode = ImageExporter::Custom;
    }
    if (options.contains("transparent")) {
        exportOptions.backgroundMode = options["transparent"].toBool() ?
                                      ImageExporter::Transparent : ImageExporter::White;
    }

    return m_imageExporter->exportToPixmap(exportOptions);
}

QImage WhiteBoard::exportToQImage(const QSize& size, const QVariantMap& options)
{
    if (!m_imageExporter) {
        qWarning() << "ImageExporter not initialized";
        return QImage();
    }

    ImageExporter::ExportOptions exportOptions;
    if (!size.isEmpty()) {
        exportOptions.imageSize = size;
    }

    // 从QVariantMap设置选项
    if (options.contains("backgroundColor")) {
        exportOptions.customBackgroundColor = options["backgroundColor"].value<QColor>();
        exportOptions.backgroundMode = ImageExporter::Custom;
    }
    if (options.contains("transparent")) {
        exportOptions.backgroundMode = options["transparent"].toBool() ?
                                      ImageExporter::Transparent : ImageExporter::White;
    }

    return m_imageExporter->exportToImage(exportOptions);
}

bool WhiteBoard::exportMultipleFormats(const QString& baseFilePath, const QStringList& formats,
                                      const QSize& size, const QVariantMap& options)
{
    if (!m_imageExporter) {
        qWarning() << "ImageExporter not initialized";
        return false;
    }

    QList<ImageExporter::ImageFormat> imageFormats;
    for (const QString& format : formats) {
        QString formatLower = format.toLower();
        if (formatLower == "png") imageFormats.append(ImageExporter::PNG);
        else if (formatLower == "jpg" || formatLower == "jpeg") imageFormats.append(ImageExporter::JPG);
        else if (formatLower == "bmp") imageFormats.append(ImageExporter::BMP);
        else if (formatLower == "tiff" || formatLower == "tif") imageFormats.append(ImageExporter::TIFF);
        else if (formatLower == "svg") imageFormats.append(ImageExporter::SVG);
        else if (formatLower == "pdf") imageFormats.append(ImageExporter::PDF);
    }

    ImageExporter::ExportOptions exportOptions;
    if (!size.isEmpty()) {
        exportOptions.imageSize = size;
    }

    return m_imageExporter->exportMultipleFormats(baseFilePath, imageFormats, exportOptions);
}

void WhiteBoard::updatePenFromType()
{
    if (!m_view) return;

    QPen pen;
    QColor color = m_currentColor;

    switch (m_currentPenType) {
    case PenType::Solid:
        // 实线笔：正常颜色，实线样式
        pen = QPen(color, m_currentLineWidth, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin);
        break;

    case PenType::Dashed:
        // 虚线笔：正常颜色
        pen = QPen(color, m_currentLineWidth, Qt::CustomDashLine, Qt::RoundCap, Qt::RoundJoin);
        pen.setDashPattern(DrawingConstants::CUSTOM_DASH_PATTERN);
        break;

    case PenType::Highlighter:
        // 荧光笔：半透明颜色，较粗线条，实线样式
        color.setAlphaF(0.5);  // 设置50%透明度
        pen = QPen(color, m_currentLineWidth * 2.0, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin);
        break;
    }

    // 应用透明度设置
    if (m_currentOpacity < 1.0) {
        QColor penColor = pen.color();
        penColor.setAlphaF(penColor.alphaF() * m_currentOpacity);
        pen.setColor(penColor);
    }

    m_view->setPen(pen);
}





// 图片插入API实现
bool WhiteBoard::insertImage(const QString& imagePath, const QPointF& position,
                           qreal displayWidth, qreal maxHeight)
{
    if (!m_view || !m_view->scene()) {
        qWarning() << "WhiteBoard: 视图或场景未初始化";
        return false;
    }

    if (imagePath.isEmpty()) {
        qWarning() << "WhiteBoard: 图片路径为空";
        return false;
    }

    // 使用默认值或传入的参数
    qreal finalWidth = displayWidth > 0 ? displayWidth : m_defaultImageWidth;
    qreal finalMaxHeight = maxHeight > 0 ? maxHeight : m_defaultImageMaxHeight;

    // 创建DrawItem（图片类型）
    // todo  imageItem可能存在内存泄露
    DrawItem* imageItem = new DrawItem(imagePath, position, finalWidth, finalMaxHeight);

    if (!imageItem->isImageValid()) {
        qWarning() << "WhiteBoard: 无法加载图片" << imagePath;
        delete imageItem;
        return false;
    }

    qDebug() << "WhiteBoard: DrawItem图片创建成功，显示尺寸:" << imageItem->displaySize();

    // 通过统一命令系统添加图片，支持撤销/重做
    CommandManager* cmdManager = getCommandManager();
    if (cmdManager && cmdManager->isInitialized()) {
        qDebug() << "WhiteBoard: 通过CommandManager添加图片";
        cmdManager->addItem(imageItem);
    } else {
        // 回退到直接添加
        qDebug() << "WhiteBoard: 直接添加图片到场景";
        WhiteBoardScene* scene = qobject_cast<WhiteBoardScene*>(m_view->scene());
        if (scene) {
            scene->addGraphicsItem(imageItem);
        } else {
            qWarning() << "WhiteBoard: 场景转换失败";
        }
    }

    qDebug() << "WhiteBoard: 成功插入图片" << imagePath
             << "位置:" << position
             << "尺寸:" << imageItem->displaySize();

    return true;
}

bool WhiteBoard::insertImageAtCenter(const QString& imagePath, qreal displayWidth, qreal maxHeight)
{
    if (!m_view) {
        return false;
    }

    // 计算视图中心位置
    QRectF viewRect = m_view->rect();
    QPointF centerPos = viewRect.center();

    // 转换为场景坐标
    QPointF scenePos = m_view->mapToScene(centerPos.toPoint());

    // 预先计算图片尺寸以确定正确的中心位置
    qreal finalWidth = displayWidth > 0 ? displayWidth : m_defaultImageWidth;
    qreal finalMaxHeight = maxHeight > 0 ? maxHeight : m_defaultImageMaxHeight;

    // 加载图片获取实际尺寸
    QPixmap tempPixmap(imagePath);
    if (!tempPixmap.isNull()) {
        QSizeF originalSize = tempPixmap.size();

        // 按宽度缩放
        qreal scale = finalWidth / originalSize.width();
        qreal scaledHeight = originalSize.height() * scale;

        // 如果高度超过限制，按高度重新缩放
        if (scaledHeight > finalMaxHeight) {
            scale = finalMaxHeight / originalSize.height();
            finalWidth = originalSize.width() * scale;
            scaledHeight = finalMaxHeight;
        }

        // 调整位置，使图片中心位于指定点
        scenePos.setX(scenePos.x() - finalWidth / 2.0);
        scenePos.setY(scenePos.y() - scaledHeight / 2.0);
    }

    return insertImage(imagePath, scenePos, displayWidth, maxHeight);
}

void WhiteBoard::setDefaultImageWidth(qreal width)
{
    if (width > 0) {
        m_defaultImageWidth = adaptSize(width);
    }
}

void WhiteBoard::setDefaultImageMaxHeight(qreal maxHeight)
{
    if (maxHeight > 0) {
        m_defaultImageMaxHeight = adaptSize(maxHeight);
    }
}

qreal WhiteBoard::getDefaultImageWidth() const
{
    return m_defaultImageWidth;
}

qreal WhiteBoard::getDefaultImageMaxHeight() const
{
    return m_defaultImageMaxHeight;
}

// 性能检测API实现
void WhiteBoard::enableDrawingPerformanceProfiler(bool enabled)
{
    if (m_view) {
        m_view->enableDrawingPerformanceProfiler(enabled);
    }
}

void WhiteBoard::resetDrawingPerformanceStats()
{
    if (m_view) {
        m_view->resetDrawingPerformanceStats();
    }
}

void WhiteBoard::printDrawingPerformanceSummary()
{
    if (m_view) {
        m_view->printDrawingPerformanceSummary();
    }
}
