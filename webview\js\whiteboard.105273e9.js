import { S as SelectionPlugin, a as IPencilModes, e as Cn, H as HlWhiteboard, f as initControls, M as MultiPencilPlugin, P as PencilPlugin, b as EraserPlugin, L as LinePlugin, A as ArrowPlugin, C as CirclePlugin, c as EllipsePlugin, R as RectPlugin, T as TrianglePlugin, d as RightTrianglePlugin, h as ImagePlugin, j as IHlWhiteboardEvents, I as IHistoryPluginEvents } from "./hlwhiteboard.b54f17ff.js";
import { a1 as defineStore, s as shallowRef } from "./bootstrap.ab073eb8.js";
import { Z as ZmqUtils } from "./axios.234f7bed.js";
import { a as QT_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
const DEFAULT_ERASER_HEIGHT = 82;
const DEFAULT_BRUSH_SIZE = 2;
var WhiteboardThemeEnum = /* @__PURE__ */ ((WhiteboardThemeEnum2) => {
  WhiteboardThemeEnum2["TRANSPARENT"] = "transparent";
  WhiteboardThemeEnum2["WHITE"] = "white";
  WhiteboardThemeEnum2["GREEN"] = "green";
  return WhiteboardThemeEnum2;
})(WhiteboardThemeEnum || {});
const whiteboardThemeMap = {
  [
    "transparent"
    /* TRANSPARENT */
  ]: {
    theme: "transparent",
    backgroundColor: "transparent",
    // 透明主题背景色为白色
    brushColor: "red"
    // 透明主题无默认铅笔颜色
  },
  [
    "white"
    /* WHITE */
  ]: {
    theme: "white",
    backgroundColor: "#fff",
    // 白色主题背景色为白色
    brushColor: "#000"
    // 白色主题默认铅笔颜色为黑色
  },
  [
    "green"
    /* GREEN */
  ]: {
    theme: "green",
    backgroundColor: "#234641",
    // 绿色主题背景色为浅绿色
    brushColor: "red"
    // 绿色主题默认铅笔颜色为深绿色
  }
};
function isHasChildTools(tool) {
  if (!tool) {
    return false;
  }
  const { childTools } = tool;
  if (childTools && childTools.length > 0) {
    return true;
  }
  return false;
}
var WhiteboardSizeEnum = /* @__PURE__ */ ((WhiteboardSizeEnum2) => {
  WhiteboardSizeEnum2[WhiteboardSizeEnum2["LARGE"] = 5] = "LARGE";
  WhiteboardSizeEnum2[WhiteboardSizeEnum2["MEDIUM"] = 3.5] = "MEDIUM";
  WhiteboardSizeEnum2[WhiteboardSizeEnum2["SMALL"] = 2] = "SMALL";
  return WhiteboardSizeEnum2;
})(WhiteboardSizeEnum || {});
var WhiteboardColorEnum = /* @__PURE__ */ ((WhiteboardColorEnum2) => {
  WhiteboardColorEnum2["WHITE"] = "#ffffff";
  WhiteboardColorEnum2["RED"] = "#E93B26";
  WhiteboardColorEnum2["BLUE"] = "#0497E8";
  WhiteboardColorEnum2["YELLOW"] = "#F8BD18";
  WhiteboardColorEnum2["CUSTOM"] = "custom";
  return WhiteboardColorEnum2;
})(WhiteboardColorEnum || {});
const useWhiteboardStore = defineStore("zhhb-whiteboard", {
  state: () => ({
    /** fabirc实例 */
    canvas: shallowRef(null),
    /** 白板操作实例 */
    hlWhiteboard: shallowRef(null),
    /** 当前激活的绘图工具 */
    currentDrawTool: SelectionPlugin.drawTool,
    /** 历史记录列表 */
    historyList: shallowRef([]),
    /** 历史记录索引 */
    historyIndex: 0,
    /** 是否是在绘制状态 */
    isDrawStatus: true,
    /** 工具列表 */
    tools: shallowRef([]),
    /** 一级激活的工具 */
    currentTool: shallowRef(null),
    /** 一级激活工具索引 */
    currentToolIndex: -1,
    /** 二级激活的工具 */
    currentSecondTool: shallowRef(null),
    /** 二级激活的工具 */
    currentSecondToolIndex: -1,
    /** 工具弹窗是否显示 */
    toolPopoverVisible: false,
    /** 弹窗显示延迟计时器 */
    showTimeoutIndex: shallowRef(null),
    /** 画笔颜色类型 */
    brushColorType: WhiteboardColorEnum.RED,
    /** 画笔颜色（用于自定义颜色） */
    brushColor: WhiteboardColorEnum.RED.toString(),
    /** 画笔大小类型 */
    brushSizeType: WhiteboardSizeEnum.SMALL,
    /** 画笔类型 */
    brushType: IPencilModes.SOLID,
    /** 橡皮擦大小 */
    eraserSizeType: WhiteboardSizeEnum.SMALL
  }),
  getters: {
    /**
     * 是否是画笔
     * @param param0 当前工具
     */
    isPencilTool({ currentTool }) {
      if (!currentTool) {
        return false;
      }
      return currentTool.name === "画笔";
    },
    /**
     * 是否是橡皮擦
     * @param param0 当前工具
     */
    isEraserTool({ currentTool }) {
      if (!currentTool) {
        return false;
      }
      return currentTool.name === "橡皮";
    },
    /**
     * 橡皮大小class名称
     * @param param0 橡皮大小类型
     * @returns
     */
    eraserSizeClassName({ eraserSizeType }) {
      switch (eraserSizeType) {
        case WhiteboardSizeEnum.LARGE:
          return "large";
        case WhiteboardSizeEnum.MEDIUM:
          return "medium";
        case WhiteboardSizeEnum.SMALL:
          return "small";
        default:
          return "medium";
      }
    }
  },
  actions: {
    /**
     * 初始化白板
     * @param canvas canvasdom对象
     */
    initWhiteboard(canvasEl, canvasOptions, hlWhiteboardOptions) {
      this.canvas = new Cn(canvasEl, canvasOptions);
      this.hlWhiteboard = new HlWhiteboard(this.canvas, hlWhiteboardOptions);
      const hlWhiteboard = this.hlWhiteboard;
      const canvas = this.canvas;
      initControls(canvas);
      hlWhiteboard.use(MultiPencilPlugin);
      hlWhiteboard.use(PencilPlugin);
      hlWhiteboard.use(EraserPlugin);
      hlWhiteboard.use(LinePlugin);
      hlWhiteboard.use(ArrowPlugin);
      hlWhiteboard.use(CirclePlugin);
      hlWhiteboard.use(EllipsePlugin);
      hlWhiteboard.use(RectPlugin);
      hlWhiteboard.use(TrianglePlugin);
      hlWhiteboard.use(RightTrianglePlugin);
      hlWhiteboard.use(ImagePlugin);
      hlWhiteboard.switchDrawTool(SelectionPlugin.drawTool);
      hlWhiteboard.on(IHlWhiteboardEvents.SWITCH_DRAW_TOOL, (drawTool) => {
        this.currentDrawTool = drawTool;
      });
      hlWhiteboard.on(IHistoryPluginEvents.HISTORY_INDEX_CHANGE, (index) => {
        this.historyIndex = index;
      });
      hlWhiteboard.on(IHistoryPluginEvents.HISTORY_LIST_CHANGE, (history) => {
        this.historyList = history;
      });
      this.setEraserSizeType(this.eraserSizeType);
      return {
        hlWhiteboard,
        canvas
      };
    },
    /**
     * 销毁白板
     */
    destroy() {
      if (this.hlWhiteboard) {
        this.hlWhiteboard.destroy();
        const isDrawStatus = this.isDrawStatus;
        this.$reset();
        this.isDrawStatus = isDrawStatus;
      }
    },
    /**
     * 切换工具
     * @param drawTool 工具名称
     * @param options 工具参数
     * @returns 是否切换成功
     */
    switchDrawTool(drawTool, options) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.switchDrawTool(drawTool, options);
      return true;
    },
    /**
     * 设置画布尺寸
     * @param width 宽度
     * @param height 高度
     * @returns 是否设置成功
     */
    setCanvasSize(width, height) {
      if (!this.canvas) {
        console.warn("canvas is not init");
        return false;
      }
      const { canvas } = this;
      canvas.setDimensions({
        width,
        height
      });
      return true;
    },
    /**
     * 清空画布
     * @returns 是否清除成功
     */
    clearCanvas() {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.clear();
      return true;
    },
    /**
     * 设置画笔尺寸
     * @param width 画笔尺寸
     * @returns
     */
    _setBrushWidth(width) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.setBrushWidth(width);
      return true;
    },
    /**
     * 设置画笔颜色
     * @param width 画笔尺寸
     * @returns
     */
    _setBrushColor(color) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.setBrushColor(color);
      this.brushColor = color;
      return true;
    },
    /**
     * 设置画笔大小类型
     * @param type
     */
    setBrushSizeType(type) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      switch (this.brushType) {
        case IPencilModes.MARKER:
          this._setBrushWidth(type * 4);
          break;
        default:
          this._setBrushWidth(type);
          break;
      }
      this.brushSizeType = type;
      return true;
    },
    /**
     * 设置画笔颜色类型
     * @param type 颜色类型
     * @param brushColor 自定义颜色值
     */
    setBrushColorType(type, brushColor) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      switch (type) {
        case WhiteboardColorEnum.CUSTOM:
          if (brushColor) {
            this._setBrushColor(brushColor);
            this.brushColorType = type;
          }
          break;
        default:
          this._setBrushColor(type);
          this.brushColorType = type;
      }
      return true;
    },
    /**
     * 设置画笔类型
     * @param type 画笔类型
     */
    setBrushType(type) {
      const hlWhiteboard = this.hlWhiteboard;
      if (!hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      switch (type) {
        case IPencilModes.SOLID:
          hlWhiteboard.switchDrawTool(MultiPencilPlugin.drawTool);
          hlWhiteboard.switchMultiPencilMode(type);
          break;
        default:
          hlWhiteboard.switchDrawTool(PencilPlugin.drawTool);
          hlWhiteboard.switchPencilMode(type);
          break;
      }
      this.brushType = type;
      this.setBrushSizeType(this.brushSizeType);
      this.setBrushColorType(this.brushColorType, this.brushColor);
      return true;
    },
    /**
     * 设置橡皮擦尺寸
     * @param width 橡皮擦尺寸
     * @returns
     */
    setEraserSize(size) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.setEraserSize(size);
      return true;
    },
    /**
     * 设置橡皮擦尺寸类型
     * @param type 橡皮擦尺寸类型
     */
    setEraserSizeType(type) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      switch (type) {
        case WhiteboardSizeEnum.LARGE:
          this.setEraserSize(DEFAULT_ERASER_HEIGHT * 1.1);
          break;
        case WhiteboardSizeEnum.MEDIUM:
          this.setEraserSize(DEFAULT_ERASER_HEIGHT);
          break;
        case WhiteboardSizeEnum.SMALL:
          this.setEraserSize(DEFAULT_ERASER_HEIGHT * 0.8);
          break;
      }
      this.eraserSizeType = type;
      return true;
    },
    /**
     * 撤销
     * @returns 是否撤销成功
     */
    revoke() {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.back();
      return true;
    },
    /**
     * 恢复
     * @returns 是否恢复成功
     */
    restore() {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      this.hlWhiteboard.restore();
      return true;
    },
    /**
     * 导入历史记录数据
     * @param historyList 历史记录列表
     * @param index 历史记录索引
     * @returns 是否导入成功
     */
    async importHistory(historyList, index) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      await this.hlWhiteboard.importHistory(historyList, index);
      return true;
    },
    /**
     * 添加图片到白板
     * @param url 图片地址
     * @param options 图片参数
     * @returns 是否添加成功
     */
    async addImageFormUrl(url, options) {
      if (!this.hlWhiteboard) {
        console.warn("hlWhiteboard is not init");
        return false;
      }
      await this.hlWhiteboard.addImageFormUrl(url, options);
      return true;
    },
    /**
     * 设置绘制状态
     * @param status 绘制状态
     */
    setDrawStatus(status) {
      this.isDrawStatus = status;
    },
    /**
     * 初始化工具列表
     * @param tools 工具列表
     */
    initTools(tools) {
      this.tools = tools;
      this.resetTool();
      this.resetSecondTool();
    },
    /**
     * 清空工具列表
     */
    clearTools() {
      this.tools = [];
      this.resetTool();
      this.resetSecondTool();
    },
    /**
     * 重置工具列表
     */
    resetTool() {
      this.currentTool = null;
      this.currentToolIndex = -1;
    },
    resetSecondTool() {
      this.currentSecondTool = null;
      this.currentSecondToolIndex = -1;
    },
    /**
     * 查找工具
     * @param name 工具名称
     */
    findTools(name) {
      if (!this.tools) {
        console.warn("tools is not init");
        return null;
      }
      for (let i = 0; i < this.tools.length; i++) {
        const item = this.tools[i];
        if (item.name === name) {
          return {
            activeTool: item,
            activeToolIndex: i,
            activeSecondToolIndex: -1,
            activeSecondTool: null
          };
        }
        if (!item.childTools) {
          continue;
        }
        for (let j = 0; j < item.childTools.length; j++) {
          const childItem = item.childTools[j];
          if (childItem.name !== name) {
            continue;
          }
          return {
            activeTool: item,
            activeToolIndex: i,
            activeSecondToolIndex: j,
            activeSecondTool: childItem
          };
        }
      }
      return null;
    },
    /**
     * 显示工具浮窗
     */
    showToolPopover(immediate = false) {
      const { showTimeoutIndex } = this;
      if (showTimeoutIndex) {
        clearTimeout(showTimeoutIndex);
        this.showTimeoutIndex = null;
      }
      if (!this.toolPopoverVisible) {
        if (immediate) {
          this.toolPopoverVisible = true;
        } else {
          this.showTimeoutIndex = setTimeout(() => {
            this.toolPopoverVisible = true;
          }, 200);
        }
      }
    },
    /**
     * 隐藏工具浮窗
     */
    hideToolPopover() {
      const { showTimeoutIndex } = this;
      if (showTimeoutIndex) {
        clearTimeout(showTimeoutIndex);
        this.showTimeoutIndex = null;
      }
      if (this.toolPopoverVisible) {
        this.toolPopoverVisible = false;
      }
    },
    /**
     * 激活工具
     * @param tool 工具
     * @param index 工具索引
     */
    activeTool(tool, index) {
      if (!tool.isCanActive) {
        typeof tool.handler === "function" && tool.handler();
        return;
      }
      if (this.currentTool === tool) {
        this.switchDrawTool(SelectionPlugin.drawTool);
        this.setDrawStatus(true);
        this.hideToolPopover();
        setTimeout(() => {
          this.resetTool();
          this.resetSecondTool();
        });
        typeof tool.cancel === "function" && tool.cancel();
        return;
      }
      this.setDrawStatus(true);
      this.resetTool();
      this.resetSecondTool();
      const hasChildTools = isHasChildTools(tool);
      if (hasChildTools) {
        this.currentTool = tool;
        this.currentToolIndex = index;
        this.showToolPopover(true);
      } else {
        this.hideToolPopover();
        setTimeout(() => {
          this.currentTool = tool;
          this.currentToolIndex = index;
        });
      }
      typeof tool.handler === "function" && tool.handler();
    },
    /**
     * 激活二级工具
     * @param tool 二级工具
     * @param index 二级工具索引
     */
    activeSecondTool(tool, index) {
      if (!tool.isCanActive) {
        typeof tool.handler === "function" && tool.handler();
        return;
      }
      if (this.currentSecondTool === tool) {
        this.switchDrawTool(SelectionPlugin.drawTool);
        this.resetSecondTool();
        return;
      }
      this.currentSecondTool = tool;
      this.currentSecondToolIndex = index;
      typeof tool.handler === "function" && tool.handler();
    },
    /**
     * 激活绘制选择工具
     */
    activeDrawSelectTool() {
      ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_ACTIVE_DRAW_SELECTION);
      this.setDrawStatus(true);
    },
    /**
     * 激活课件选择工具
     */
    activeCoursewareSelectTool() {
      ZmqUtils.sendRequest(QT_RENDERER_MESSAGE_TYPE.QT_ACTIVE_RESOURCE_SELECTION);
      this.setDrawStatus(false);
    }
  }
});
export {
  DEFAULT_BRUSH_SIZE as D,
  WhiteboardThemeEnum as W,
  WhiteboardColorEnum as a,
  WhiteboardSizeEnum as b,
  DEFAULT_ERASER_HEIGHT as c,
  isHasChildTools as i,
  useWhiteboardStore as u,
  whiteboardThemeMap as w
};
