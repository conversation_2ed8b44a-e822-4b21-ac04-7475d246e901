#include "GraphicsItemState.h"
#include "../graphics/DrawItem.h"
#include "../core/WhiteBoardScene.h"
#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>

// GraphicsItemState 实现

GraphicsItemState::GraphicsItemState()
    : exists(false)
    , position(0, 0)
    , zValue(0)
    , opacity(1.0)
    , visible(true)
    , enabled(true)
{
}

GraphicsItemState::GraphicsItemState(const QString& id, bool itemExists)
    : itemId(id)
    , exists(itemExists)
    , position(0, 0)
    , zValue(0)
    , opacity(1.0)
    , visible(true)
    , enabled(true)
{
}

GraphicsItemState GraphicsItemState::fromGraphicsItem(QGraphicsItem* item)
{
    GraphicsItemState state;

    if (!item) {
        return state;
    }

    // 尝试获取DrawItem的ID
    DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
    if (drawItem) {
        state.itemId = drawItem->itemId();

        // 对于DrawItem，我们需要捕获当前的变换状态
        // 如果有变换，先临时烘焙到几何数据中来获取最终状态
        QTransform currentTransform = item->transform();
        QPointF currentPos = item->pos();

        // 直接保存当前状态，变换信息将在applyToGraphicsItem中处理
        state.itemData = drawItem->toJson();
        state.transform = currentTransform;
        state.position = currentPos;
    } else {
        // 对于非DrawItem，生成临时ID
        state.itemId = QString::number(reinterpret_cast<quintptr>(item), 16);
        state.position = item->pos();
        state.transform = item->transform();
    }

    state.exists = true;
    state.zValue = item->zValue();
    state.opacity = item->opacity();
    state.visible = item->isVisible();
    state.enabled = item->isEnabled();

    return state;
}

void GraphicsItemState::applyToGraphicsItem(QGraphicsItem* item) const
{
    if (!item) {
        return;
    }

    // 如果是DrawItem，先应用完整数据，然后烘焙变换
    DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
    if (drawItem && !itemData.isEmpty()) {
        // 先恢复几何数据
        drawItem->fromJson(itemData);

        // 然后烘焙transform到几何数据中（不包括position）
        if (!transform.isIdentity()) {
            drawItem->bakeTransform(transform, QPointF()); // position传空值

            // 重置transform为单位矩阵，但保持position
            item->setPos(position);
            item->setTransform(QTransform());
        } else {
            // 如果没有transform变换，直接应用位置和变换
            item->setPos(position);
            item->setTransform(transform);
        }
    } else {
        // 对于非DrawItem，使用原来的逻辑
        item->setPos(position);
        item->setTransform(transform);
    }

    // 应用其他属性
    item->setZValue(zValue);
    item->setOpacity(opacity);
    item->setVisible(visible);
    item->setEnabled(enabled);
}

QJsonObject GraphicsItemState::toJson() const
{
    QJsonObject json;
    json["itemId"] = itemId;
    json["exists"] = exists;
    json["itemData"] = itemData;
    
    // 场景属性
    QJsonObject posObj;
    posObj["x"] = position.x();
    posObj["y"] = position.y();
    json["position"] = posObj;
    
    // 变换矩阵
    QJsonArray transformArray;
    transformArray.append(transform.m11());
    transformArray.append(transform.m12());
    transformArray.append(transform.m13());
    transformArray.append(transform.m21());
    transformArray.append(transform.m22());
    transformArray.append(transform.m23());
    transformArray.append(transform.m31());
    transformArray.append(transform.m32());
    transformArray.append(transform.m33());
    json["transform"] = transformArray;
    
    json["zValue"] = zValue;
    json["opacity"] = opacity;
    json["visible"] = visible;
    json["enabled"] = enabled;
    
    return json;
}

void GraphicsItemState::fromJson(const QJsonObject& json)
{
    itemId = json["itemId"].toString();
    exists = json["exists"].toBool();
    itemData = json["itemData"].toObject();
    
    // 位置
    QJsonObject posObj = json["position"].toObject();
    position = QPointF(posObj["x"].toDouble(), posObj["y"].toDouble());
    
    // 变换矩阵
    QJsonArray transformArray = json["transform"].toArray();
    if (transformArray.size() == 9) {
        transform = QTransform(
            transformArray[0].toDouble(), transformArray[1].toDouble(), transformArray[2].toDouble(),
            transformArray[3].toDouble(), transformArray[4].toDouble(), transformArray[5].toDouble(),
            transformArray[6].toDouble(), transformArray[7].toDouble(), transformArray[8].toDouble()
        );
    }
    
    zValue = json["zValue"].toDouble();
    opacity = json["opacity"].toDouble();
    visible = json["visible"].toBool();
    enabled = json["enabled"].toBool();
}

bool GraphicsItemState::operator==(const GraphicsItemState& other) const
{
    return itemId == other.itemId &&
           exists == other.exists &&
           itemData == other.itemData &&
           position == other.position &&
           transform == other.transform &&
           qFuzzyCompare(zValue, other.zValue) &&
           qFuzzyCompare(opacity, other.opacity) &&
           visible == other.visible &&
           enabled == other.enabled;
}

bool GraphicsItemState::operator!=(const GraphicsItemState& other) const
{
    return !(*this == other);
}

QString GraphicsItemState::toString() const
{
    return QString("GraphicsItemState(id=%1, exists=%2, pos=%3,%4)")
           .arg(itemId)
           .arg(exists ? "true" : "false")
           .arg(position.x())
           .arg(position.y());
}

// GraphicsStateManager 实现

QList<GraphicsItemState> GraphicsStateManager::captureSceneState(const QList<QGraphicsItem*>& items)
{
    QList<GraphicsItemState> states;
    
    for (QGraphicsItem* item : items) {
        if (item) {
            states.append(GraphicsItemState::fromGraphicsItem(item));
        }
    }
    
    return states;
}

void GraphicsStateManager::applySceneState(const QList<GraphicsItemState>& states, WhiteBoardScene* scene)
{
    if (!scene) {
        qWarning() << "[STATE_MANAGER] Invalid scene for applying state";
        return;
    }

    // 收集当前场景中的所有图形项
    QList<QGraphicsItem*> currentItems = scene->items();
    QHash<QString, QGraphicsItem*> itemMap;

    for (QGraphicsItem* item : currentItems) {
        DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
        if (drawItem) {
            itemMap[drawItem->itemId()] = item;
        }
    }

    // 只处理状态列表中明确指定的图形
    for (const GraphicsItemState& state : states) {
        QGraphicsItem* existingItem = itemMap.value(state.itemId);

        if (state.exists) {
            // 图形应该存在
            if (existingItem) {
                // 更新现有图形
                state.applyToGraphicsItem(existingItem);
            } else {
                // 创建新图形
                if (!state.itemData.isEmpty()) {
                    DrawItem* newItem = new DrawItem(QPainterPath(), QPen(), QBrush(), ToolType::FreeDraw);
                    newItem->fromJson(state.itemData);
                    state.applyToGraphicsItem(newItem);
                    scene->addGraphicsItem(newItem);
                }
            }
        } else {
            // 图形应该被删除
            if (existingItem) {
                scene->removeGraphicsItem(existingItem);
                delete existingItem;
            }
        }
    }
}

GraphicsItemState GraphicsStateManager::createEmptyState(const QString& itemId)
{
    return GraphicsItemState(itemId, false);
}

GraphicsItemState GraphicsStateManager::createFullState(DrawItem* drawItem)
{
    if (!drawItem) {
        return GraphicsItemState();
    }
    
    return GraphicsItemState::fromGraphicsItem(drawItem);
}

QList<QString> GraphicsStateManager::findDifferences(const QList<GraphicsItemState>& before, 
                                                     const QList<GraphicsItemState>& after)
{
    QList<QString> differences;
    
    // 创建ID映射
    QHash<QString, GraphicsItemState> beforeMap;
    QHash<QString, GraphicsItemState> afterMap;
    
    for (const GraphicsItemState& state : before) {
        beforeMap[state.itemId] = state;
    }
    
    for (const GraphicsItemState& state : after) {
        afterMap[state.itemId] = state;
    }
    
    // 查找差异
    QSet<QString> allIds;
    for (const QString& id : beforeMap.keys()) {
        allIds.insert(id);
    }
    for (const QString& id : afterMap.keys()) {
        allIds.insert(id);
    }
    
    for (const QString& id : allIds) {
        GraphicsItemState beforeState = beforeMap.value(id);
        GraphicsItemState afterState = afterMap.value(id);
        
        if (beforeState != afterState) {
            differences.append(id);
        }
    }
    
    return differences;
}

QJsonArray GraphicsStateManager::serializeStates(const QList<GraphicsItemState>& states)
{
    QJsonArray array;
    
    for (const GraphicsItemState& state : states) {
        array.append(state.toJson());
    }
    
    return array;
}

QList<GraphicsItemState> GraphicsStateManager::deserializeStates(const QJsonArray& jsonArray)
{
    QList<GraphicsItemState> states;
    
    for (const QJsonValue& value : jsonArray) {
        if (value.isObject()) {
            GraphicsItemState state;
            state.fromJson(value.toObject());
            states.append(state);
        }
    }
    
    return states;
}
