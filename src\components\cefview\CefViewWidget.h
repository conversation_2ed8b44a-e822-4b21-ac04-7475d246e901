#ifndef CEFVIEWWIDGET_H
#define CEFVIEWWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QMouseEvent>
#include <QTimer>
#include <QPaintEvent>
#include <QPainter>
#include <QShowEvent>
#include <QHideEvent>
#include <QCefView.h>
#include <QCefSetting.h>
#include "../iconfont/iconfonts.h"
#include "src/screen_adaptation/ScreenAdaptationManager.h"

/**
 * @brief 自定义拖拽控制栏类，使用自定义绘制确保圆角效果
 */
class DragBar : public QWidget
{
    Q_OBJECT

public:
    explicit DragBar(QWidget* parent = nullptr) : QWidget(parent) {
        setFixedSize(ScreenAdaptationConstants::adaptSize(80), ScreenAdaptationConstants::adaptSize(10));
    }

protected:
    void paintEvent(QPaintEvent* event) override {
        Q_UNUSED(event)

        QPainter painter(this);
        painter.setRenderHint(QPainter::Antialiasing);

        // 设置画笔和画刷
        painter.setPen(Qt::NoPen);
        painter.setBrush(QColor(255, 255, 255, 217)); // 白色，透明度0.85

        // 绘制圆角矩形
        painter.drawRoundedRect(rect(), ScreenAdaptationConstants::adaptSize(8), ScreenAdaptationConstants::adaptSize(8));
    }
};

/**
 * @brief 封装的QCefView组件，包含底部操作栏和拖拽功能
 * 
 * 功能特性：
 * - 传入大小、位置、URL即可使用
 * - 底部操作栏不占用传入的大小（QCefView在上方，操作栏在底部）
 * - SVG按钮：关闭按钮、拖拽按钮
 * - 点击关闭按钮关闭widget
 * - 按住拖拽按钮可以拖拽整个组件
 * - 支持透明背景
 */
class CefViewWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父组件
     */
    explicit CefViewWidget(QWidget *parent, const QString &cefName, const bool showFullScreenButton = false, const bool showControlBar = false, const bool showDragBar = false);
    
    /**
     * @brief 析构函数
     */
    ~CefViewWidget();

    /**
     * @brief 设置CefView的URL
     * @param url 要加载的URL
     */
    void setUrl(const QString& url);
    
    /**
     * @brief 设置CefView的HTML内容
     * @param html HTML内容
     */
    void setHtml(const QString& html);
    
    /**
     * @brief 设置CefView的大小和位置
     * @param geometry 几何信息（不包含底部操作栏的高度）
     */
    void setCefViewGeometry(const QRect& geometry);
    
    /**
     * @brief 获取QCefView实例
     * @return QCefView指针
     */
    QCefView* cefView() const;
    
    /**
     * @brief 设置是否显示底部操作栏
     * @param visible 是否显示
     */
    void setControlBarVisible(bool visible);
    
    /**
     * 设置是否隐藏拖拽bar
     */
    void setDragBarVisible(bool visible);

    /**
     * @brief 获取底部操作栏是否可见
     * @return 是否可见
     */
    bool isControlBarVisible() const;

    /**
     * @brief 使用Qt原生方式设置widget缩放
     * @param scaleFactor 缩放因子
     */
    void setWidgetScale(qreal scaleFactor);

    /**
     * @brief 获取当前widget缩放因子
     * @return 缩放因子
     */
    qreal getWidgetScale() const;

    /**
     * @brief 自动应用DPI适配缩放
     */
    void applyAutoScale();

    /**
     * @brief 设置是否启用自动缩放
     * @param enabled 是否启用
     */
    void setAutoScaleEnabled(bool enabled);

    /**
     * @brief 关闭按钮点击槽函数
     */
    void onCloseButtonClicked();

    /**
    * @berif 全屏按钮点击槽函数
    */
    void onFullscreenButtonClicked();

    /**
     * @brief 进入全屏
     */
    void enterFullScreen(bool sendEvent = true);
    /**
     * @brief 退出全屏
     */
     void exitFullScreen();

    /**
     * @brief 设置全屏按钮可见性
     * @param visible 是否可见
     */
    void setFullscreenButtonVisible(bool visible);
    /**
     * @brief 获取全屏按钮可见性
     * @return 是否可见
     */
    bool isFullscreenButtonVisible() const;

signals:
    /**
     * @brief 关闭按钮被点击信号
     */
    void closeRequested();
    
    /**
     * @brief 拖拽开始信号
     */
    void dragStarted();
    
    /**
     * @brief 拖拽结束信号
     */
    void dragFinished();

    /**
     * @brief 窗口显示信号
     */
    void widgetShown();

    /**
     * @brief 窗口隐藏信号
     */
    void widgetHidden();

protected:
    /**
     * @brief 显示事件
     * @param event 显示事件
     */
    void showEvent(QShowEvent* event) override;

    /**
     * @brief 隐藏事件
     * @param event 隐藏事件
     */
    void hideEvent(QHideEvent* event) override;

    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent* event) override;
    
    /**
     * @brief 鼠标移动事件
     */
    void mouseMoveEvent(QMouseEvent* event) override;
    
    /**
     * @brief 鼠标释放事件
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

    /**
     * @brief 窗口大小改变事件
     */
    void resizeEvent(QResizeEvent* event) override;

private slots:

private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();
    
    /**
     * @brief 初始化QCefView
     */
    void initializeCefView(const QString& cefName);
    
    /**
     * @brief 初始化拖拽控制栏
     */
    void initializeControlBar();
    
    /**
     * @brief 创建SVG按钮
     * @param iconPath SVG图标路径
     * @param size 按钮大小
     * @return 创建的按钮
     */
    QPushButton* createSvgButton(const QString& iconPath, const QSize& size = QSize(24, 24));
    
    /**
     * @brief 检查点是否在拖拽句柄内
     * @param pos 鼠标位置
     * @return 是否在拖拽句柄内
     */
    bool isInDragHandle(const QPointF& pos) const;

    /**
     * @brief 更新控制栏位置
     */
    void updateControlBarPosition();

    /**
     * @brief 加载结束
     */
    void onLoadEnd();

    /**
     * @brief 加载失败时显示错误UI
     */
    void showErrorUI();

    /**
     * @brief 隐藏错误UI
     */
    void hideErrorUI();

    /**
     * @brief 初始化错误UI
     */
    void initLoadErrorUI();

private:
    // UI组件
    QVBoxLayout* m_mainLayout;          ///< 主布局
    QCefView* m_cefView;                ///< QCefView组件
    QWidget* m_bottomContainer;         ///< 底部控制栏容器
    QWidget* m_controlBar;              ///< 底部控制栏
    DragBar* m_dragBar;                 ///< 顶部拖拽控制栏
    QHBoxLayout* m_controlLayout;       ///< 控制栏布局
    QPushButton* m_closeButton;         ///< 关闭按钮
    QPushButton* m_fullscreenButton = nullptr;    ///< 全屏按钮
    QSpacerItem* m_fullscreenSpacing = nullptr; ///< 全屏按钮左侧间距
    QPushButton* m_dragButton;          ///< 拖拽按钮（已废弃）

    // 控制控件是否展示
    bool m_showControlBar;
    bool m_showDragBar;

    // 全屏相关
    bool m_showFullScreenButton;
	// 全屏前的几何信
    QRect m_previousGeometry;
    QRect m_cefViewPreviousGeometry;
	// 是否全屏
    bool m_isFullScreen = false;
    // 全屏前widget 和 parent的关系记录
    QMap<QWidget*, QWidget*> m_widgetParentMap;

    // 拖拽相关
    bool m_isDragging;                  ///< 是否正在拖拽
    bool m_dragStarted;                 ///< 是否开始拖拽
    QPoint m_dragStartPos;              ///< 拖拽开始位置
    QPoint m_windowStartPos;            ///< 窗口开始位置
    QString m_cefName;                  ///< cef名称，用于唯一标识一个cefView
    qreal m_scaleFactor;                ///< 当前缩放因子
    bool m_autoScaleEnabled;            ///< 是否启用自动缩放

    // 页面加载失败相关
    QPushButton* m_loadErrorRefreshBtn;
    QPushButton* m_loadErrorCloseBtn;
    /**
     * 请求cef加载的url
     */
    QString url;

    // 配置
    static constexpr int CONTROL_BAR_CONTAINER_HEIGHT = 30;  ///< 控制栏容器高度（包含顶部间距）
    static constexpr int DRAG_THRESHOLD  = 3;                 ///< 拖拽阈值
    static constexpr int DRAG_AREA_MARGIN = 30;              ///< 拖拽区域边距，用于进一步扩大可拖拽范围

};

#endif // CEFVIEWWIDGET_H
