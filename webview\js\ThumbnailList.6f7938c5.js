import { _ as _export_sfc, a as Bridge, B as BridgeZmqUtils, c as setBusinessInfoWidget, l as logger, d as initLoggerWidget, p as pinia } from "./index.f30fa4a1.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, r as ref, w as watch, b as openBlock, m as createElementBlock, j as createBaseVNode, k as normalizeClass, H as toDisplayString, D as createCommentVNode, F as Fragment, R as renderList, n as nextTick, e as createBlock, f as withCtx, p as createVNode, z as onMounted, $ as onUnmounted, ad as createApp } from "./bootstrap.ab073eb8.js";
import "./base.676dddc3.js";
import { E as ElImage } from "./el-image-viewer.10cb6477.js";
import { S as SvgIcon } from "./index.9044e7d8.js";
import { O as OfficeViewSDK } from "./OfficeViewSDK.88a56c0a.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import { a as showError } from "./toastWidget.67d1d2c8.js";
import { i as isPositiveInteger } from "./hlwhiteboard.b54f17ff.js";
import { R as ResourceTypeEnum } from "./IResource.516d6004.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.1c1fd1ce.js";
import "./typescript.063380fa.js";
import "./index.4e3d2b08.js";
import "./position.2e4d825a.js";
const _hoisted_1$1 = { class: "thumbnail-list" };
const _hoisted_2 = { class: "thumbnail-list-title-wrap flex flex-ac" };
const _hoisted_3 = { class: "thumbnail-scrollbar" };
const _hoisted_4 = ["onClick"];
const _hoisted_5 = { class: "thumbnail-item__num" };
const _hoisted_6 = {
  key: 1,
  class: "thumbnail-item__active"
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    currentIndex: { default: 0 },
    title: { default: "" },
    images: { default: () => [] },
    visible: { type: Boolean, default: false }
  },
  emits: ["select"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const imageListRef = ref();
    const titleRef = ref();
    const emits = __emit;
    const small = ref(false);
    const onClickImage = (index) => {
      emits("select", index);
    };
    watch(
      () => [props.visible, props.images],
      (visible) => {
        if (visible) {
          nextTick(() => {
            if (imageListRef.value && imageListRef.value[props.currentIndex]) {
              imageListRef.value[props.currentIndex].scrollIntoView({
                block: "center",
                behavior: "smooth"
              });
            }
            if (titleRef.value && props.title) {
              const element = titleRef.value;
              small.value = element.scrollHeight > element.clientHeight;
            }
          });
        }
      }
    );
    watch(
      () => props.currentIndex,
      () => {
        nextTick(() => {
          if (props.visible) {
            if (imageListRef.value && imageListRef.value[props.currentIndex]) {
              imageListRef.value[props.currentIndex].scrollIntoView({
                block: "center",
                behavior: "smooth"
              });
            }
          }
        });
      }
    );
    return (_ctx, _cache) => {
      const _component_el_image = ElImage;
      return openBlock(), createElementBlock("div", _hoisted_1$1, [
        createBaseVNode("div", _hoisted_2, [
          _ctx.title ? (openBlock(), createElementBlock("div", {
            key: 0,
            ref_key: "titleRef",
            ref: titleRef,
            class: normalizeClass(["thumbnail-list-title text-ellipsis-2", {
              "thumbnail-list-title--small": small.value
            }])
          }, toDisplayString(_ctx.title), 3)) : createCommentVNode("", true)
        ]),
        createBaseVNode("div", _hoisted_3, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(props.images, (image, index) => {
            return openBlock(), createElementBlock("div", {
              ref_for: true,
              ref_key: "imageListRef",
              ref: imageListRef,
              key: index,
              class: "thumbnail-item flex",
              onClick: ($event) => onClickImage(index)
            }, [
              createBaseVNode("div", _hoisted_5, toDisplayString(index + 1), 1),
              props.visible ? (openBlock(), createBlock(_component_el_image, {
                key: 0,
                src: image,
                class: "thumbnail-item__image",
                fit: "scale-down",
                lazy: ""
              }, {
                toolbar: withCtx(() => _cache[0] || (_cache[0] = [])),
                _: 2
              }, 1032, ["src"])) : createCommentVNode("", true),
              _ctx.currentIndex === index ? (openBlock(), createElementBlock("div", _hoisted_6, [
                createVNode(SvgIcon, { "icon-class": "select" })
              ])) : createCommentVNode("", true)
            ], 8, _hoisted_4);
          }), 128))
        ])
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_c9701fff_lang = "";
const ThumbnailList = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-c9701fff"]]);
const _hoisted_1 = { class: "thumbnail-list-container" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const currentIndex = ref(0);
    const visible = ref(true);
    const thumbnailList = ref([]);
    const currentResource = ref();
    const genThumbnailList = (resource) => {
      try {
        if (!resource) {
          logger.warn("【缩略图】", "resource is null");
          return [];
        }
        const { type, info, pages } = resource;
        if (!(type && [ResourceTypeEnum.PDF, ResourceTypeEnum.PPT].includes(type)) || !info) {
          return [];
        }
        const thumbnailUrl = OfficeViewSDK.getThumbnailUrl({
          fileId: "",
          documentId: "",
          documentUrl: "",
          documentName: info?.documentName,
          transThirdName: info?.transThirdName,
          thumbnailUrl: info?.thumbnailUrl
        });
        if (!thumbnailUrl) {
          return [];
        }
        if (Array.isArray(pages)) {
          return pages.map((_, index) => {
            return `${thumbnailUrl}${index + 1}.jpg?x-oss-process=image/resize,w_394`;
          });
        }
        return [];
      } catch (error) {
        logger.error("【WhiteboardToolbars】", "生成缩略图列表失败", error);
        return [];
      }
    };
    const onSelect = (index) => {
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_JUMP_TO_PAGE, {
        index
      }).then(() => {
        logger.info("【ThumbnailList】", "页面跳转成功", index);
        currentIndex.value = index;
      }).catch((error) => {
        logger.error("【WhiteboardToolbars】", "页面跳转失败", index, error);
        showError("跳转失败");
      });
    };
    const onInit = async () => {
      try {
        console.log("【ThumbnailList】", "初始化操作");
        const data = await BridgeZmqUtils.callEelectronRenderer(
          CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS
        );
        if (data) {
          currentResource.value = data.currentResource;
          currentIndex.value = data.currentResourcePageIndex || 0;
          thumbnailList.value = genThumbnailList(data.currentResource);
        } else {
          logger.info("【ThumbnailList】", "初始化参数为空", data);
        }
      } catch (e) {
        logger.error("【ThumbnailList】", "获取保存板书参数失败", e);
      }
    };
    const onChangePage = (data) => {
      const pageSlide = data.data?.pageSlide - 1;
      if (data.data?.resourceId !== currentResource.value?.id) {
        onInit();
        return;
      }
      if (!isPositiveInteger(pageSlide)) {
        logger.warn("【缩略图】", "翻页索引异常", data);
        return false;
      }
      if (pageSlide !== currentIndex.value) {
        currentIndex.value = pageSlide;
      }
      return true;
    };
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
    });
    onMounted(() => {
      const bridge = Bridge.getInstance();
      bridge.on("init", onInit);
      bridge.on(CEF_RENDERER_MESSAGE_TYPE.THUMBNAIL_RESOURCE_PAGE_CHANGE, onChangePage);
      onInit();
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
      }).catch((e) => {
        logger.error("【缩略图】", "获取当前上课信息失败", e);
      });
    });
    onUnmounted(() => {
      const bridge = Bridge.getInstance();
      bridge.off("init", onInit);
      bridge.off(CEF_RENDERER_MESSAGE_TYPE.THUMBNAIL_RESOURCE_PAGE_CHANGE, onChangePage);
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createVNode(ThumbnailList, {
          ref: "thumbnailListPopoverRef",
          title: currentResource.value?.info?.documentName || "",
          visible: visible.value,
          images: thumbnailList.value,
          "current-index": currentIndex.value,
          onSelect
        }, null, 8, ["title", "visible", "images", "current-index"])
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_2f919413_lang = "";
const index_vue_vue_type_style_index_1_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-2f919413"]]);
initLoggerWidget();
calcHtmlFontSize(true);
initLoggerWidget().finally(() => {
  const app = createApp(App);
  app.use(pinia).mount("#app");
});
