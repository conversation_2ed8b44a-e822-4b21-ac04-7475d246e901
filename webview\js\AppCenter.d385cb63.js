import { B as BridgeZmqUtils, Z as ZMQ_MESSAGE_TYPE, l as logger, _ as _export_sfc, a as Bridge, c as setBusinessInfoWidget, d as initLoggerWidget, p as pinia } from "./index.f30fa4a1.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { d as defineComponent, b as openBlock, m as createElementBlock, j as createBaseVNode, H as toDisplayString, I as withModifiers, r as ref, w as watch, z as onMounted, p as createVNode, f as withCtx, F as Fragment, R as renderList, u as unref, e as createBlock, ad as createApp } from "./bootstrap.ab073eb8.js";
/* empty css                            */import { S as Swiper, P as Pagination, a as SwiperSlide, b as Swiper$1 } from "./swiper-slide.69c69280.js";
import { s as showError } from "./toast.866a5bf5.js";
import { C as CEF_RENDERER_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./index.9044e7d8.js";
const _hoisted_1$1 = ["src"];
const _hoisted_2 = ["title"];
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    appItem: {}
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    const onOpenApp = async (item) => {
      try {
        await BridgeZmqUtils.callEelectron(ZMQ_MESSAGE_TYPE.WIN_MINIMIZE, {});
        await BridgeZmqUtils.callEelectron(ZMQ_MESSAGE_TYPE.RUN_APP, {
          executablePath: item.shortcut
        });
        logger.info("应用打开成功", item);
        emits("close");
      } catch (e) {
        showError("应用打开失败");
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: "apps-item flex flex-v flex-ac flex-jc",
        onClick: _cache[0] || (_cache[0] = withModifiers(($event) => onOpenApp(_ctx.appItem), ["stop"]))
      }, [
        createBaseVNode("img", {
          class: "apps-item__icon",
          src: _ctx.appItem.iconBase64
        }, null, 8, _hoisted_1$1),
        createBaseVNode("div", {
          class: "apps-item__name text-ellipsis",
          title: _ctx.appItem.name
        }, toDisplayString(_ctx.appItem.name), 9, _hoisted_2)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_334e173a_lang = "";
const AppItemWidget = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-334e173a"]]);
const apps = ref([]);
function useStartMenuApps(flush = false) {
  const refresh = async () => {
    try {
      const bridge = Bridge.getInstance();
      const data = await bridge.call("getWindowsApps", {
        excludedFolders: [],
        excludedFiles: [],
        filterKeywords: []
      });
      if (Array.isArray(data)) {
        const list = [];
        data.forEach((item) => {
          const { name, path, shortcut, iconBase64 } = item || {};
          list.push({
            name,
            path,
            shortcut,
            iconBase64
          });
        });
        apps.value = list;
      }
    } catch (e) {
      logger.warn("【addon】获取开始菜单应用失败: ", e);
    }
  };
  if (flush || apps.value.length === 0) {
    refresh();
  }
  return {
    apps,
    refresh
  };
}
const _hoisted_1 = { class: "apps-list flex flex-wrap" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  setup(__props) {
    const { apps: apps2 } = useStartMenuApps();
    Swiper.use([Pagination]);
    const appsList = ref([]);
    const onClickBackground = () => {
      const bridge = Bridge.getInstance();
      bridge?.callVoid("close");
    };
    watch(
      apps2,
      (newVal) => {
        const list = [];
        newVal.forEach((item, index) => {
          const outerIndex = Math.floor(index / 32);
          if (!list[outerIndex]) {
            list[outerIndex] = [];
          }
          const outerList = list[outerIndex];
          outerList.push(item);
        });
        appsList.value = list;
      },
      {
        immediate: true
      }
    );
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
    });
    onMounted(() => {
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher.saasSchoolId,
          campusId: classTeacher.saasCampusId,
          classId: classTeacher.saasClassId,
          className: classTeacher.saasClassName,
          subjectCode: classTeacher.saasSubjectCode,
          subjectName: classTeacher.saasSubjectName,
          userId: classTeacher.saasUserId
        });
      }).catch((e) => {
        logger.error("【应用中心】", "获取当前上课信息失败", e);
      });
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: "apps-center-wrap",
        onClick: onClickBackground
      }, [
        createVNode(unref(Swiper$1), {
          pagination: "",
          class: "apps-center"
        }, {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(appsList.value, (list) => {
              return openBlock(), createBlock(unref(SwiperSlide), null, {
                default: withCtx(() => [
                  createBaseVNode("div", _hoisted_1, [
                    (openBlock(true), createElementBlock(Fragment, null, renderList(list, (item) => {
                      return openBlock(), createBlock(AppItemWidget, {
                        key: item.name,
                        "app-item": item,
                        onClose: onClickBackground
                      }, null, 8, ["app-item"]);
                    }), 128))
                  ])
                ]),
                _: 2
              }, 1024);
            }), 256))
          ]),
          _: 1
        })
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_lang = "";
const index_vue_vue_type_style_index_1_scoped_464d37f9_lang = "";
const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-464d37f9"]]);
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
