#include "mainwindow.h"
#include "src/global.h"
#include "src/constants/AppConstants.h"

#include <QApplication>
#include <QStandardPaths>
#include <QCefContext.h>
#include <QCefConfig.h>
#include <QMessageBox>
#include <QSurfaceFormat>
#include <QOpenGLContext>
#include "src/diagnostic/log/Log.h"
#include "src/diagnostic/crash/CrashReport.h"
#include <QDir>
#include <utils/LoadDllUtils.h>

#include "src/components/zmq/server/ZmqServer.h"
#include "src/screen_adaptation/ScreenAdaptationManager.h"
#include "src/components/iconfont/iconfontmanager.h"
#include "src/components/singleinstance/singleinstancemanager.h"

int main(int argc, char *argv[])
{
    // 初始化日志系统
    Log::init();
    // 初始化崩溃报告
    CrashReport::init();
    // ==================== OpenGL加速配置 ====================
    // 方案1：启用Qt的OpenGL光栅化引擎（立即生效）
    QApplication::setAttribute(Qt::AA_UseOpenGLES, false);  // 使用桌面OpenGL
    QApplication::setAttribute(Qt::AA_UseDesktopOpenGL, true);  // 强制使用桌面OpenGL

    // 配置全局OpenGL格式（增强抗锯齿配置）
    QSurfaceFormat format;

    // 高精度缓冲区配置
    format.setDepthBufferSize(32);        // 提升深度缓冲区精度
    format.setStencilBufferSize(8);
    format.setRedBufferSize(8);           // 明确设置颜色缓冲区精度
    format.setGreenBufferSize(8);
    format.setBlueBufferSize(8);
    format.setAlphaBufferSize(8);

    // 动态MSAA配置：根据屏幕分辨率调整
    QScreen* primaryScreen = QApplication::primaryScreen();
    int msaaSamples = 4;  // 默认4x MSAA
    if (primaryScreen) {
        QSize screenSize = primaryScreen->size();
        qreal devicePixelRatio = primaryScreen->devicePixelRatio();
        bool is4K = (screenSize.width() >= 3840 && screenSize.height() >= 2160);
        bool isHighDPI = devicePixelRatio > 1.5;

        if (is4K || isHighDPI) {
            msaaSamples = 16;  // 4K/高DPI屏幕使用16x MSAA
            qDebug() << "[MAIN] 检测到4K/高DPI屏幕，启用16x MSAA全局抗锯齿";
        } else {
            msaaSamples = 8;   // 普通屏幕使用8x MSAA
        }
    }
    format.setSamples(msaaSamples);

    // OpenGL版本和配置
    format.setProfile(QSurfaceFormat::CoreProfile);
    format.setVersion(3, 3);
    format.setSwapBehavior(QSurfaceFormat::DoubleBuffer);
    format.setSwapInterval(1);  // 启用垂直同步

    // 设置为默认格式
    QSurfaceFormat::setDefaultFormat(format);

    qDebug() << "[MAIN] 全局OpenGL格式配置完成，MSAA级别:" << msaaSamples;

    QApplication a(argc, argv);

    // 需在创建QApplication之后调用
    bool exitApp = LoadDllUtils::loadAppDlls(argc, argv);
    if (exitApp) {
        return 0;
    }

    // 应用正常退出时，刷新日志
    Log::flushOnExit(&a);

    // 强制启用OpenGL光栅化引擎（这是关键！）
    a.setAttribute(Qt::AA_ForceRasterWidgets, false);  // 禁用强制光栅化

    qDebug() << "[OPENGL] OpenGL加速配置完成";
    qDebug() << "[OPENGL] OpenGL版本:" << QOpenGLContext::openGLModuleType();

    // 设置应用程序信息，用于单实例管理
    a.setApplicationName(AppConstants::APP_NAME);
    a.setApplicationVersion("1.0.0");
    a.setOrganizationName("HL");
    a.setOrganizationDomain("hl.com");

    // 初始化单实例管理器
    SingleInstanceManager singleInstance(AppConstants::APP_NAME);

    // 检查是否为第一个实例
    if (!singleInstance.isFirstInstance()) {
        // 不是第一个实例，尝试激活已存在的实例
        qDebug() << "检测到应用程序已在运行，尝试激活现有实例...";

        // 发送激活消息给第一个实例
        QString activationMessage = "ACTIVATE_WINDOW";
        if (argc > 1) {
            // 如果有命令行参数，一并发送
            QStringList args;
            for (int i = 1; i < argc; ++i) {
                args << QString::fromLocal8Bit(argv[i]);
            }
            activationMessage += "|" + args.join("|");
        }

        if (singleInstance.sendMessageToFirstInstance(activationMessage)) {
            qDebug() << "激活消息发送成功，退出当前实例";
            return 0;
        } else {
            qWarning() << "无法连接到现有实例，尝试强制启动新实例";
            // 强制清理可能的残留资源并重新检查
            singleInstance.forceCleanupAndRestart();

            // 重新检查是否成功成为第一个实例
            if (!singleInstance.isFirstInstance()) {
                qWarning() << "强制清理失败，仍有其他实例在运行，退出当前实例";
                return 0;
            }
            qDebug() << "强制清理成功，当前实例将作为第一个实例启动";
        }
    }

    // 这是第一个实例，启动本地服务器监听后续实例
    if (!singleInstance.startServer()) {
        qWarning() << "启动单实例服务器失败";
    }


    // 初始化ZMQ服务器
    // ipc放到用户目录下，避免权限问题
    QString userDir = QStandardPaths::writableLocation(QStandardPaths::HomeLocation);
    if (!QDir(userDir).exists()) {
        qDebug() << "【ipc】 Directory does not exist." << userDir;
    }
    // 命名规则：.hl-ipc-${项目名}
    QString projectDir = ".hl-ipc-hl-white-board";
    QString fullPath = QDir(userDir).filePath(projectDir);
    QString ipcPath = QString("ipc://%1").arg(QDir::cleanPath(fullPath));
    qDebug() << "【ipc】 ipcPath:" << ipcPath;
    ZmqServer server(ipcPath.toStdString());


    // 启用触屏支持
    // 启用鼠标事件转触控事件
    a.setAttribute(Qt::AA_SynthesizeTouchForUnhandledMouseEvents, true);

    // 设置应用程序退出策略：当最后一个窗口关闭时退出
    // 但由于CEF的特殊性，我们需要手动控制退出时机
    a.setQuitOnLastWindowClosed(false);

    // 初始化日志系统
    QString logPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs/application.log";
    Logger::instance()->initialize(logPath, LogLevel::DEBUG);

    // 初始化屏幕适配管理器
    ScreenAdaptationConstants::ScreenAdaptationManager::instance()->initialize();

    // 初始化IconFontManager
    IconFontManager* iconFontManager = IconFontManager::instance();
    if (!iconFontManager->loadDefaultIconFont()) {
        qWarning() << "IconFontManager初始化失败";
    } else {
        qDebug() << "IconFontManager初始化成功";
    }

    // 初始化QCefContext
    QCefConfig config;
    // 设置用户代理
    config.setUserAgent(AppConstants::APP_NAME);
    // 设置日志级别
    config.setLogLevel(QCefConfig::LOGSEVERITY_DEFAULT);
    // 设置JSBridge对象名称
    config.setBridgeObjectName("CallBridge");
    // 设置远程调试端口（开发时使用）
    config.setRemoteDebuggingPort(9000);
    // 设置背景色为透明
    config.setBackgroundColor(Qt::transparent);
    // 禁用无窗口渲染模式以确保QCefView正常显示
    config.setWindowlessRenderingEnabled(true);
    // 添加命令行参数
    config.addCommandLineSwitch("use-mock-keychain");
    config.addCommandLineSwitchWithValue("renderer-process-limit", "1");
    /**
    *   "--disable-web-security "          // 禁用同源策略
    *    "--allow-file-access-from-files "  // 允许文件访问
    *    "--allow-universal-access-from-files"; // 允许跨域文件访问
    */
    config.addCommandLineSwitch("--disable-web-security");
    config.addCommandLineSwitch("--allow-file-access-from-files");
    config.addCommandLineSwitch("--allow-universal-access-from-files");

    qDebug() << "[MAIN] 全局QCefContext配置完成";

    // 创建QCefContext实例
    QCefContext cefContext(&a, argc, argv, &config);

    qDebug() << "[MAIN] 全局QCefContext实例创建完成";

    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    MainWindow w;
    w.show();

    qDebug() << "[MAIN] 主窗口创建完成";

    // 设置单实例消息处理回调
    singleInstance.setMessageReceivedCallback([&w](const QString& message) {
        qDebug() << "收到来自其他实例的消息:" << message;

        // 解析消息
        QStringList parts = message.split("|");
        if (!parts.isEmpty() && parts.first() == "ACTIVATE_WINDOW") {
            // 激活窗口
            w.show();
            w.raise();
            w.activateWindow();

            // 如果窗口被最小化，恢复窗口
            if (w.isMinimized()) {
                w.showNormal();
            }

            // 确保窗口在最前面
            w.setWindowState((w.windowState() & ~Qt::WindowMinimized) | Qt::WindowActive);

            qDebug() << "窗口已激活";

            // 处理命令行参数（如果有的话）
            if (parts.size() > 1) {
                QStringList args = parts.mid(1);
                qDebug() << "接收到命令行参数:" << args;
                // 这里可以根据需要处理命令行参数
                // 例如：打开特定文件、切换到特定模式等
            }
        }
    });

    // 连接信号处理
    QObject::connect(&singleInstance, &SingleInstanceManager::newInstanceDetected, [&w]() {
        qDebug() << "检测到新实例尝试启动";
        // 可以在这里添加额外的处理逻辑，比如显示通知等
    });

    int result = a.exec();
    
    // 确保CEF上下文正确清理
    qDebug() << "应用程序退出，清理CEF上下文";
    
    return result;
}
