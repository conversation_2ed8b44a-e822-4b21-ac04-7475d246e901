#ifndef SELECTIONUIMANAGER_H
#define SELECTIONUIMANAGER_H

#include <QObject>
#include <QGraphicsItem>
#include <QList>
#include <QMap>
#include <QPainter>
#include <QPointF>
#include <QTimer>
#include "SelectionUITypes.h"

class WhiteBoardWidget;
class DrawItem;

/**
 * @brief 选择UI管理器
 * 
 * 负责管理选择框UI的显示、交互和渲染
 * 与LassoTool集成，在选择完成后显示选择框UI
 */
class SelectionUIManager : public QObject
{
    Q_OBJECT

public:
    explicit SelectionUIManager(QObject* parent = nullptr);
    ~SelectionUIManager();

    /**
     * @brief 设置关联的视图
     */
    void setView(WhiteBoardWidget* view);

    /**
     * @brief 显示选择框UI
     * @param selectedItems 选中的图形项列表
     */
    void showSelectionUI(const QList<QGraphicsItem*>& selectedItems);

    /**
     * @brief 隐藏选择框UI
     */
    void hideSelectionUI();

    /**
     * @brief 更新选择框UI（当选中项发生变化时）
     * @param selectedItems 新的选中图形项列表
     */
    void updateSelectionUI(const QList<QGraphicsItem*>& selectedItems);

    /**
     * @brief 在视图上绘制选择框UI
     * @param painter 绘制器
     */
    void paintSelectionUI(QPainter* painter);

    /**
     * @brief 命中测试
     * @param point 测试点
     * @param outAnchorType 输出锚点类型
     * @param outToolbarAction 输出工具栏操作
     * @return 命中测试结果
     */
    HitTestResult hitTest(const QPointF& point, AnchorType& outAnchorType, ToolbarAction& outToolbarAction);

    /**
     * @brief 交互操作处理
     */
    void startInteraction(const QPointF& startPoint, HitTestResult hitType, AnchorType anchorType, ToolbarAction toolbarAction);
    void updateInteraction(const QPointF& currentPoint);
    void finishInteraction(const QPointF& endPoint);
    void cancelInteraction();

    /**
     * @brief 检查是否有选择框UI显示
     */
    bool isSelectionUIVisible() const { return m_isVisible; }

    /**
     * @brief 获取当前选中的图形项
     */
    QList<QGraphicsItem*> getSelectedItems() const { return m_selectedItems; }

    /**
     * @brief 设置选择框属性
     */
    void setSelectionBoxProperties(const SelectionBoxProperties& properties);

    /**
     * @brief 获取选择框属性
     */
    SelectionBoxProperties getSelectionBoxProperties() const { return m_properties; }

    /**
     * @brief 强制清理选择状态
     * @details 立即隐藏选择UI，将所有选中图形移回历史层，清空选中状态
     * 用于工具切换、开始新绘制等场景的状态清理
     */
    void forceCleanupSelection();

    /**
     * @brief 设置是否显示命中区域（调试用）
     * @param show 是否显示命中区域
     */
    void setShowHitAreas(bool show);

    /**
     * @brief 检查是否需要清理选择状态
     * @param ignoreInteraction 是否忽略当前交互状态
     * @return 如果需要清理返回true
     */
    bool shouldCleanupSelection(bool ignoreInteraction = false) const;

signals:
    /**
     * @brief 锚点被点击
     * @param anchorType 锚点类型
     * @param selectedItems 选中的图形项
     */
    void anchorClicked(AnchorType anchorType, const QList<QGraphicsItem*>& selectedItems);

    /**
     * @brief 工具栏操作被点击
     * @param action 工具栏操作
     * @param selectedItems 选中的图形项
     */
    void toolbarActionClicked(ToolbarAction action, const QList<QGraphicsItem*>& selectedItems);

    /**
     * @brief 选择框UI可见性改变
     * @param visible 是否可见
     */
    void selectionUIVisibilityChanged(bool visible);

private slots:
    /**
     * @brief 处理视图更新
     */
    void onViewUpdated();

private:
    /**
     * @brief 计算选择框边界
     * @param items 图形项列表
     * @return 边界矩形
     */
    QRectF calculateBoundingRect(const QList<QGraphicsItem*>& items);

    /**
     * @brief 确定锚点类型
     * @param items 图形项列表
     * @return 锚点类型
     */
    SelectAnchorType determineAnchorType(const QList<QGraphicsItem*>& items);

    /**
     * @brief 生成锚点列表
     * @param anchorType 锚点类型
     * @param boundingRect 边界矩形
     * @return 锚点信息列表
     */
    QList<AnchorInfo> generateAnchors(SelectAnchorType anchorType, const QRectF& boundingRect);

    /**
     * @brief 绘制选择框边框
     */
    void drawSelectionBox(QPainter* painter);

    /**
     * @brief 绘制锚点
     */
    void drawAnchors(QPainter* painter);

    /**
     * @brief 绘制旋转锚点和图标
     */
    void drawRotationElements(QPainter* painter);

    /**
     * @brief 绘制底部工具栏
     */
    void drawToolbar(QPainter* painter);

    /**
     * @brief 绘制单个锚点
     */
    void drawAnchor(QPainter* painter, const AnchorInfo& anchor);

    /**
     * @brief 绘制工具栏图标
     */
    void drawToolbarIcon(QPainter* painter, const QRectF& iconRect, const QString& iconPath);

    /**
     * @brief 检查点是否在锚点内
     */
    bool isPointInAnchor(const QPointF& point, const AnchorInfo& anchor);

    /**
     * @brief 检查点是否在工具栏内
     */
    bool isPointInToolbar(const QPointF& point, ToolbarAction& outAction);

    /**
     * @brief 初始化默认工具栏图标
     */
    void initializeDefaultToolbarIcons();

    /**
     * @brief 图层管理方法
     */
    void moveSelectedItemsToActiveLayer();
    void moveSelectedItemsToHistoryLayer();

    /**
     * @brief 变换操作辅助方法
     */
    void saveOriginalTransforms();
    void restoreOriginalTransforms();
    void performMove(const QPointF& delta);
    void performResize(const QPointF& totalDelta);
    void performRotation(const QPointF& currentPoint);
    void performLineEndpointDrag(const AnchorType anchorType, const QPointF &totalDelta);  // 直线/箭头端点拖拽
    void executeToolbarAction(ToolbarAction action);
    void updateSelectionBoxDuringInteraction();

    /**
     * @brief 缩放操作辅助方法
     */
    QPointF calculateScalingAnchorPoint(AnchorType anchorType, const QRectF& bounds) const;
    QPointF getOriginalDragPoint(AnchorType anchorType, const QRectF& bounds) const;
    void resetResizeStaticVariables();

    /**
     * @brief 安全删除选中的图形项
     * @details 使用命令系统删除，支持撤销/重做
     */
    void deleteSelectedItemsSafely();

    /**
     * @brief 直接删除图形项（回退方案）
     * @param items 要删除的图形项列表
     */
    void performDirectDeletion(const QList<QGraphicsItem*>& items);

    /**
     * @brief 创建变换命令
     * @param interactionType 交互类型
     * @param originalTransforms 原始变换矩阵
     * @param originalPositions 原始位置
     */
    void createTransformCommand(InteractionState interactionType,
                                const QMap<QGraphicsItem *, QTransform> &originalTransforms,
                                const QMap<QGraphicsItem *, QPointF> &originalPositions,
                                const QMap<QGraphicsItem *, QJsonObject> &originalJsons);

    /**
     * @brief 变换完成后更新选择UI
     */
    void updateSelectionUIAfterTransform(InteractionState oldState);

    /**
     * @brief 判断是否是固定比例图形（正圆、正方形）
     * @param item 图形项
     * @return 是否是固定比例图形
     */
    bool isFixedRatioShape(DrawItem* item) const;

    /**
     * @brief 判断是否需要显示旋转元素
     * @return 是否需要显示旋转元素
     */
    bool determineShowRotation();

private:
    WhiteBoardWidget* m_view;                     // 关联的视图
    QList<QGraphicsItem*> m_selectedItems;     // 选中的图形项
    SelectionBoxProperties m_properties;       // 选择框属性
    QList<AnchorInfo> m_anchors;               // 锚点列表
    bool m_isVisible;                          // 是否可见
    QPointF m_rotationAnchorPos;               // 旋转锚点位置
    QRectF m_toolbarRect;                      // 工具栏区域

    // 交互状态
    InteractionState m_interactionState;       // 当前交互状态
    QPointF m_interactionStartPoint;           // 交互起始点
    QPointF m_lastInteractionPoint;            // 上次交互点
    AnchorType m_activeAnchorType;             // 活动锚点类型
    ToolbarAction m_activeToolbarAction;       // 活动工具栏操作

    // 变换状态保存
    QMap<QGraphicsItem*, QTransform> m_originalTransforms;  // 原始变换矩阵
    QMap<QGraphicsItem*, QPointF> m_originalPositions;      // 原始位置
    QMap<QGraphicsItem*, QJsonObject> m_originalJsons;      // 原始JSON数据

};

#endif // SELECTIONUIMANAGER_H
