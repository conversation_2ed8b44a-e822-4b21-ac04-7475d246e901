#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include "src/components/whiteboardview/WhiteboardView.h"

#include <QCloseEvent>
#include <QApplication>
#include <QScreen>
#include <QTimer>
#include <QDebug>
#include <QCefContext.h>
#include <nlohmann/json.hpp>

#include "src/diagnostic/devtools/DevToolsWindow.h"
#include "src/components/jsbridge/handler/JSBridgeHandlerFactory.h"
#include "src/components/zmq/handler/ZmqHandlerFactory.h"
#include "src/components/zmq/server/ZmqServer.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_whiteboardView(nullptr)
    , m_transparencyEnabled(false)    // 默认不穿透
    , m_currentTool(ToolType::Lasso)  // 默认选择工具
{
    ui->setupUi(this);

    setWindowTitle(tr("HL电子白板"));

    // 设置窗口
    setupWindow();

    // 初始化白板视图
    setupWhiteboardView();

    // 连接信号和槽
    connectSignalsAndSlots();

    // 初始化穿透模式（默认不穿透）
    updateTransparency();

    // 初始化ZMQ
    setupZMQ();
}

MainWindow::~MainWindow()
{
    // 确保CEF资源被正确清理
    if (m_whiteboardView) {
        // 强制清理WhiteboardView中的CEF组件
        delete m_whiteboardView;
        m_whiteboardView = nullptr;
    }

    delete ui;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    qDebug() << "MainWindow::closeEvent - 开始关闭应用程序";

    // 首先隐藏主窗口
    hide();

    // 确保所有CEF视图被正确关闭
    if (m_whiteboardView) {
        // 强制清理CEF组件
        qDebug() << "MainWindow::closeEvent - 清理WhiteboardView";
        m_whiteboardView->hide();

        // 给CEF一些时间来清理资源
        QTimer::singleShot(100, [this, event]() {
            qDebug() << "MainWindow::closeEvent - 延迟关闭完成";
            event->accept();
            QApplication::quit();
        });

        // 暂时忽略关闭事件，等待CEF清理完成
        event->ignore();
        return;
    }

    event->accept();
    QApplication::quit();
}

void MainWindow::setupWindow()
{
    // 设置窗口为全屏无边框模式，并置顶
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);

    // 设置透明背景
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);
    setStyleSheet("QMainWindow { background-color: transparent; }");

    // 隐藏菜单栏和状态栏
    menuBar()->hide();
    statusBar()->hide();

    // 获取屏幕尺寸并设置全屏
    QScreen* screen = QApplication::primaryScreen();
    if (screen) {
        setGeometry(screen->geometry());
    }
    showFullScreen();
}

void MainWindow::setupWhiteboardView()
{
    // 创建白板视图组件
    m_whiteboardView = new WhiteboardView(this);

    // 设置白板视图为中央窗口部件
    setCentralWidget(m_whiteboardView);

    // 确保白板视图填充整个窗口
    m_whiteboardView->setGeometry(rect());
    m_whiteboardView->show();
}

void MainWindow::setupZMQ() {

    ZmqServer::instance()->start();

    // 初始化JSBridgeHandler
    JSBridgeHandlerFactory::autoRegister();
    // 初始化ZmqHandler
    ZmqHandlerFactory::autoRegister();
}

void MainWindow::connectSignalsAndSlots()
{
    if (!m_whiteboardView) {
        return;
    }

    // 连接工具变化信号，实现穿透模式自动切换
    connect(m_whiteboardView, &WhiteboardView::currentToolChanged,
            this, &MainWindow::onCurrentToolChanged);
}

void MainWindow::onCurrentToolChanged(ToolType toolType)
{
    m_currentTool = toolType;

    // 根据工具类型自动切换穿透模式
    bool shouldEnableTransparency = shouldEnableTransparencyForTool(toolType);

    if (m_transparencyEnabled != shouldEnableTransparency) {
        setTransparencyEnabled(shouldEnableTransparency);

        // 输出调试信息
        qDebug() << "工具切换：" << static_cast<int>(toolType)
                 << "穿透模式：" << (shouldEnableTransparency ? "启用" : "禁用");
    }
}

bool MainWindow::shouldEnableTransparencyForTool(ToolType toolType) const
{
    switch (toolType) {
    case ToolType::PassThrough:
        // 穿透工具：启用穿透
        return true;
    default:
        // 未知工具：默认禁用穿透
        return false;
    }
}

void MainWindow::updateTransparentRegions()
{
    // 简化逻辑：由于WhiteboardView使用子控件，不需要复杂的区域计算
    // 穿透模式的控制完全由工具类型决定
    if (!m_transparencyEnabled) {
        m_transparentRegion = QRegion(); // 清空穿透区域
        return;
    }

    // 启用穿透时，设置整个屏幕为穿透区域
    // 子控件会自动处理自己的鼠标事件
    QScreen* screen = QApplication::primaryScreen();
    if (screen) {
        m_transparentRegion = QRegion(screen->geometry());
    }
}

void MainWindow::setTransparencyEnabled(bool enabled)
{
    m_transparencyEnabled = enabled;
    updateTransparency();
}

bool MainWindow::event(QEvent* event)
{
    switch (event->type()) {
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonRelease:
    case QEvent::MouseButtonDblClick:
    case QEvent::MouseMove: {
        QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);
        if (isPointTransparent(mouseEvent->pos())) {
            return false; // 让事件穿透到下层应用
        }
        break;
    }
    case QEvent::WindowActivate:
        // 不要强制置顶，让FloatMenuWidget保持在上层
        // raise();
        break;
    case QEvent::Resize:
        // 窗口大小变化时，更新白板视图大小
        if (m_whiteboardView) {
            m_whiteboardView->setGeometry(rect());
            m_whiteboardView->onWindowSizeChanged();
        }
        break;
    default:
        break;
    }

    return QMainWindow::event(event);
}

void MainWindow::setTransparentRegion(const QRegion& region)
{
    m_transparentRegion = region;
    updateTransparency();
}

void MainWindow::addTransparentShape(const QRect& rect, QRegion::RegionType shapeType)
{
    QRegion newRegion(rect, shapeType);
    m_transparentRegion = m_transparentRegion.united(newRegion);
    updateTransparency();
}

void MainWindow::updateTransparency()
{
    if (m_transparencyEnabled) {
        // 启用穿透模式 - 设置完全透明背景
        setAttribute(Qt::WA_TranslucentBackground, true);
        setAttribute(Qt::WA_NoSystemBackground, true);
        setStyleSheet("QMainWindow { background-color: transparent; }");
    } else {
        // 禁用穿透 - 设置接近透明的背景让窗口能接收所有事件
        setAttribute(Qt::WA_TranslucentBackground, true);
        setAttribute(Qt::WA_NoSystemBackground, false);
        setStyleSheet("QMainWindow { background-color: rgba(255, 255, 255, 2); }");
    }

    // 更新穿透区域
    updateTransparentRegions();

    update();
}

bool MainWindow::isPointTransparent(const QPoint& pos) const
{
    if (!m_transparencyEnabled) {
        return false;
    }
    return m_transparentRegion.contains(pos);
}


void MainWindow::keyPressEvent(QKeyEvent *event)
{
    // 仅debug版本生效
#ifdef QT_DEBUG
    if (event->modifiers() == Qt::ControlModifier && event->key() == Qt::Key_F12) {
        DevToolsWindow *devToolsWindow = new DevToolsWindow(this);
        devToolsWindow->show();
        devToolsWindow->raise();
        devToolsWindow->activateWindow();
        return;
    }
#endif // QT_DEBUG

    QMainWindow::keyPressEvent(event);
}
