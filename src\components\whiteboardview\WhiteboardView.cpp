#include "WhiteboardView.h"
#include "../../whiteboard/core/WhiteBoardWidget.h"
#include "../floatmenu/core/FloatMenuWidget.h"
#include "../../global.h"
#include "../toast/Toast.h"
#include "../../utils/UIRefreshHelper.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QScrollArea>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QResizeEvent>
#include <QMoveEvent>
#include <QShowEvent>
#include <QApplication>
#include <QCoreApplication>
#include <QScreen>
#include <QDebug>
#include <QDateTime>
#include <QCefView.h>
#include <QCefSetting.h>
#include <QCefContext.h>
#include <QCoreApplication>
#include <QDir>
#include <string>
#include <iostream>
#include <src/utils/WindowUtils.h>

#include "../cefview/CefViewWidget.h"
#include "ZIndexManager.h"
#include "../sidebar/SideBarWidget.h"
#include "src/components/jsbridge/JSBridge.h"
#include "../sidebar/ToolboxPopup.h"
#include "../bottombar/BottomBarWidget.h"
#include "src/components/zmq/server/ZmqServer.h"
#include "src/utils/UrlCache.h"
#include "src/components/sidebar/magnifier/MagnifierWidget.h"
#include "WhiteboardToolConfigManager.h"

WhiteboardView::WhiteboardView(QWidget *parent)
    : QWidget(parent)
    , m_whiteBoard(nullptr)
    , m_floatMenuWidget(nullptr)


    , m_isInitialized(false)
    , m_lastCanvasSize(800, 600)
    , m_zIndexManager(nullptr)
    , m_mousePassThroughEnabled(false)
    , m_sideBarWidget(nullptr)
    , m_toolboxPopup(nullptr)
    , m_bottomBarWidget(nullptr)
    , m_currentResourceKey("")
    , classroomModel("classroom")
    , m_penType(PenType::Solid)
{
    // 设置对象名称用于调试
    setObjectName("WhiteboardView");

    // 创建Z-Index管理器
    m_zIndexManager = new ZIndexManager(this);

    // 设置窗口透明度和全屏属性
    setupWindowTransparency();

    // 初始化webview
    initializeWebview();

    // 初始化白板系统
    initializeWhiteBoard();

    // 初始化底部操作栏
    initializeBottomBar();

    // 初始化浮动菜单
    initializeFloatMenu();

    // 初始化侧边栏
    initializeSideBar();

    // 连接信号和槽
    connectSignalsAndSlots();

    // 初始化UI层级管理
    initializeUILayerManagement();

    // 初始化zmq消息
    initializeZmq();

    // 初始化桥接
    initializeBridge();

    QApplication::instance()->installEventFilter(this);

    m_isInitialized = true;

    qDebug() << "WhiteboardView初始化完成";
}

WhiteboardView::~WhiteboardView()
{
    qDebug() << "销毁WhiteboardView";



    // 隐藏和清理浮动菜单
    if (m_floatMenuWidget) {
        m_floatMenuWidget->hideFloatingWindow();
        delete m_floatMenuWidget;
        m_floatMenuWidget = nullptr;
    }

    // 清理工具箱弹窗
    if (m_toolboxPopup) {
        m_toolboxPopup->hidePopup();
        delete m_toolboxPopup;
        m_toolboxPopup = nullptr;
    }





    // 清理CefViewWidget组件
    for (auto it = m_cefViews.begin(); it != m_cefViews.end(); ++it) {
        const QString& toolName = it.key();
        CefViewWidget* view = it.value();
        if (view) {
            qDebug() << QString("WhiteboardView::~WhiteboardView - 清理%1 CefViewWidget组件").arg(toolName);
            view->hide();
            view->setParent(nullptr);  // 断开父子关系
            view->deleteLater();       // 使用deleteLater确保CEF资源正确释放
        }
    }
    m_cefViews.clear();

    qDebug() << "WhiteboardView::~WhiteboardView - CEF组件清理完成";
}

FloatMenuWidget* WhiteboardView::floatMenuWidget() const
{
    return m_floatMenuWidget;
}

void WhiteboardView::showFloatMenu()
{
    if (m_floatMenuWidget) {
        m_floatMenuWidget->showFloatingWindow();
        // 使用安全的UI刷新机制
        UIRefreshHelper::instance()->safeForceRefresh(m_floatMenuWidget);
        emit floatMenuVisibilityChanged(true);
    }
}

void WhiteboardView::hideFloatMenu()
{
    if (m_floatMenuWidget) {
        qDebug() << "WhiteboardView: 隐藏浮动菜单";
        m_floatMenuWidget->hideFloatingWindow();
        // 隐藏后重新应用Z-order
        if (m_zIndexManager) m_zIndexManager->applyZOrder();
        // 使用安全的UI刷新机制
        UIRefreshHelper::instance()->safeForceRefresh(m_floatMenuWidget);
        emit floatMenuVisibilityChanged(false);
    }
}

void WhiteboardView::toggleFloatMenu()
{
    if (m_floatMenuWidget) {
        m_floatMenuWidget->toggleFloatingWindow();
        emit floatMenuVisibilityChanged(m_floatMenuWidget->isVisible());
    }
}

bool WhiteboardView::isFloatMenuVisible() const
{
    return m_floatMenuWidget ? m_floatMenuWidget->isVisible() : false;
}

void WhiteboardView::setCurrentTool(ToolType toolType)
{
    if (m_whiteBoard) {
        if (toolType == ToolType::FreeDraw) {
            switch (m_penType)
            {
                case PenType::Solid:
                    toolType = ToolType::FreeDraw;
                    break;
                case PenType::Dashed:
                    toolType = ToolType::FreeDrawDashed;
                    break;
                case PenType::Highlighter:
                    toolType = ToolType::FreeDrawHighlighter;
                    break;
                default:
                    break;
            }
        }
        m_whiteBoard->setCurrentTool(toolType);
    }
    onToolManagerToolChanged(toolType);
}

ToolType WhiteboardView::currentToolType() const
{
    if (m_whiteBoard) {
        ToolType newToolType = m_whiteBoard->getCurrentTool();
        return newToolType;
    }

    return ToolType::FreeDraw;  // 默认为自由绘制
}

void WhiteboardView::setToolColor(const QColor& color)
{
    if (m_whiteBoard) {
        m_whiteBoard->setDrawingColor(color);
    }
}

void WhiteboardView::setToolStrokeWidth(qreal width)
{
    if (m_whiteBoard) {
        m_whiteBoard->setLineWidth(width);
    }

}

void WhiteboardView::setPenType(int type)
{
    if (m_whiteBoard) {
        ToolType toolType;
        m_penType = static_cast<PenType>(type);

        switch (m_penType) {
            case PenType::Solid:
                toolType = ToolType::FreeDraw;
                break;
            case PenType::Dashed:
                toolType = ToolType::FreeDrawDashed;
                break;
            case PenType::Highlighter:
                toolType = ToolType::FreeDrawHighlighter;
                break;
            default:
                toolType = ToolType::FreeDraw;
                break;
        }

        // 设置对应的工具类型
        setCurrentTool(toolType);
    }
}

void WhiteboardView::clearCanvas()
{
    if (m_whiteBoard) {
        m_whiteBoard->clearScene();
    }
}

void WhiteboardView::saveBoard(QPoint pos)
{
    qDebug() << "WhiteboardView: 点击保存板书";
    // 检查是否有绘制轨迹
    ResourceItem currentResource = classroomModel.getCurrentResource();
    QStringList keyLists = getTraceResourceKeys(currentResource.resourceId);
    if (keyLists.isEmpty()) {
        qDebug() << "WhiteboardView: 没有绘制轨迹";
        Toast::showWarning("没有绘制轨迹");
        return;
    }
    // 检查是否已存在该工具的实例
    CefViewWidget* existingView = m_cefViews.value("退出", nullptr);
    if (existingView) {
        qDebug() << "WhiteboardView: 退出cef存在";
        if (existingView->isHidden()) {
            qDebug() << "WhiteboardView: 显示保存板书页面";
            existingView->setUrl(QString("http://domainname/src/widget/SaveBlackBoardDialog/index.html?top=%1&left=%2").arg(pos.y()).arg(pos.x()));
            existingView->show();
        } else {
            qDebug() << "WhiteboardView: 隐藏保存板书页面";
            existingView->hide();
        }
    } else {
        qDebug() << "WhiteboardView: 退出cef不存在";
        SideBarConstants::ToolInfo saveBoardConfig = WhiteboardToolConfigManager::getSaveBoardConfig();
        saveBoardConfig.url = QString("http://domainname/src/widget/SaveBlackBoardDialog/index.html?top=%1&left=%2").arg(pos.y()).arg(pos.x());
        handleCefViewWithConfig(saveBoardConfig);
    }
}

void WhiteboardView::undo()
{
    if (m_whiteBoard) {
        m_whiteBoard->undo();
        return;
    }
}

void WhiteboardView::redo()
{
    if (m_whiteBoard) {
        m_whiteBoard->redo();
        return;
    }
}

bool WhiteboardView::canUndo() const
{
    if (m_whiteBoard) {
        return m_whiteBoard->canUndo();
    }
    return false;
}

bool WhiteboardView::canRedo() const
{
    if (m_whiteBoard) {
        return m_whiteBoard->canRedo();
    }
    return false;
}



ZIndexManager* WhiteboardView::zIndexManager() const
{
    return m_zIndexManager;
}

void WhiteboardView::bringComponentToTop(QWidget* widget)
{
    if (m_zIndexManager && widget) {
        m_zIndexManager->bringToTop(widget);
        qDebug() << QString("WhiteboardView: 临时置顶组件 [%1]").arg(widget->objectName());
    }
}

void WhiteboardView::restoreComponentLevel(QWidget* widget)
{
    if (m_zIndexManager && widget) {
        m_zIndexManager->restoreOriginalLevel(widget);
        qDebug() << QString("WhiteboardView: 恢复组件层级 [%1]").arg(widget->objectName());
    }
}

void WhiteboardView::printZIndexStatus() const
{
    if (m_zIndexManager) {
        m_zIndexManager->printLevelStatus();
    } else {
        qDebug() << "WhiteboardView: ZIndexManager未初始化";
    }
}

void WhiteboardView::onWindowSizeChanged()
{
    updateCanvasSize();
}






void WhiteboardView::toggleResourceTrace(const QString& resourceKey)
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this, resourceKey]() {
            toggleResourceTrace(resourceKey);
        }, Qt::QueuedConnection);
        return;
    }
    if (m_whiteBoard) {
        if (m_whiteBoard->hasContent()) {
            m_resourceTraceMap[m_currentResourceKey] = m_whiteBoard->exportToJson();
            m_resourceImageMap[m_currentResourceKey] = m_whiteBoard->exportToQImage(this->size());
            m_whiteBoard->clearAll();
        }

        if (m_resourceTraceMap.contains(resourceKey)) {
            m_whiteBoard->importFromJson(m_resourceTraceMap[resourceKey]);
        }
    }

    // 更新当前资源key
    m_currentResourceKey = resourceKey;
    qDebug() << QString("WhiteboardView: 当前资源key已更新为: %1").arg(m_currentResourceKey);
}

QStringList WhiteboardView::getTraceResourceKeys(const QString& resourceId)
{
    qDebug() << "WhiteboardView: 获取资源key列表：" + resourceId.toStdString();
    qDebug() << QString("WhiteboardView: 获取资源key列表, resourceId: %1").arg(resourceId);
    qDebug() << "WhiteboardView currentResourceKey: " + m_currentResourceKey.toStdString();
    if (resourceId.isEmpty()) {
        return QStringList();
    }

    // 先保存当前的
    if (m_whiteBoard && m_whiteBoard->hasContent()) {
        m_resourceTraceMap[m_currentResourceKey] = m_whiteBoard->exportToJson();
    }

    if (m_resourceTraceMap.isEmpty()) {
        return QStringList();
    }

    // 返回内容不为空的资源key
    QStringList keys;
    for (auto it = m_resourceTraceMap.begin(); it != m_resourceTraceMap.end(); ++it) {
        qDebug() << "WhiteboardView it-key contains resourceId" << "key:" << it.key() << "resourceId:" << resourceId.toStdString();
        // 判断是否是该资源
        if (!it.key().contains(resourceId)) {
            continue;
        }

        // 判断该资源是否为空
        if (!it.value().empty() && it.value()["totalItemCount"].toInt() > 0) {
            keys.append(it.key());
        }
    }
    return keys;
}

QJsonArray WhiteboardView::saveResourceImages(const QJsonArray& saveRequests)
{
    // 先保存当前的
    if (m_whiteBoard && m_whiteBoard->hasContent()) {
        m_resourceImageMap[m_currentResourceKey] = m_whiteBoard->exportToQImage(this->size());
    }

    QJsonArray results;

    for (const QJsonValue& requestValue : saveRequests) {
        if (!requestValue.isObject()) {
            continue;
        }

        QJsonObject request = requestValue.toObject();
        QJsonObject result;

        // 获取请求参数
        QString index = request.value("index").toString();
        QString imagePath = request.value("imagePath").toString();
        QString imageUrl = request.value("imageUrl").toString();

        // 设置返回结果的基础信息
        result["index"] = index;
        result["imagePath"] = imagePath;
        result["imageUrl"] = imageUrl;
        result["isSuccess"] = false;

        // 检查参数有效性
        if (imagePath.isEmpty()) {
            qDebug() << QString("WhiteboardView: 保存资源图片失败，imagePath为空，index: %1").arg(index);
            results.append(result);
            continue;
        }

        QImage resourceImage = m_resourceImageMap[index];
        if (resourceImage.isNull()) {
            qDebug() << QString("WhiteboardView: 保存资源图片失败，图片为空，resourceKey: %1").arg(index);
            results.append(result);
            continue;
        }

        // 保存图片到指定路径
        QImageWriter writer(imagePath, "PNG");
        if (writer.canWrite()) {
            if (writer.write(resourceImage)) {
                result["isSuccess"] = true;
                qDebug() << QString("WhiteboardView: 资源图片保存成功，index: %1，路径: %2").arg(index).arg(imagePath);
            } else {
                LOG_ERROR(QString("WhiteboardView: 资源图片保存失败，写入错误，index: %1，路径: %2，错误: %3").arg(index).arg(imagePath).arg(writer.errorString()));
            }
        } else {
            LOG_ERROR(QString("WhiteboardView: 资源图片保存失败，不支持该格式，index: %1，路径: %2").arg(index).arg(imagePath));
        }

        results.append(result);
    }

    qDebug() << QString("WhiteboardView: 资源图片保存完成，处理 %1 个请求").arg(saveRequests.size());
    return results;
}

void WhiteboardView::moveEvent(QMoveEvent* event)
{
    QWidget::moveEvent(event);

    // 更新FloatMenuWidget相对于主窗口的位置
    if (m_floatMenuWidget) {
        m_floatMenuWidget->updatePositionRelativeToMainWindow();
    }
}

void WhiteboardView::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);

    if (m_isInitialized) {
        // 确保画布始终全屏 - 现在由updateCanvasSize处理
        updateCanvasSize();

        // 更新所有独立窗口的位置
        if (m_sideBarWidget) {
            m_sideBarWidget->updatePositionRelativeToMainWindow();
        }

        if (m_bottomBarWidget) {
            m_bottomBarWidget->updatePositionRelativeToMainWindow();
        }

        if (m_floatMenuWidget) {
            m_floatMenuWidget->updatePositionRelativeToMainWindow();
        }

        // 重新设置组件层级
        setupComponentZOrder();
    }
}

void WhiteboardView::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);

    if (m_isInitialized) {
        updateCanvasSize();

        // 确保UI组件在顶层
        ensureUIComponentsOnTop();
    }
}

bool WhiteboardView::eventFilter(QObject* obj, QEvent* event)
{
    // 只处理特定对象的事件，避免全局事件过滤
    // if (obj == this || obj == m_canvasView) {
    //     if (event->type() == QEvent::Show) {
    //         // 立即执行层级调整，不使用定时器
    //         setupComponentZOrder();
    //     }
    // }
    QVariant result = handleThumbnailHide(obj, event);
    if (result.isValid()) {
        return result.toBool();
    }

    return QWidget::eventFilter(obj, event);
}

QVariant WhiteboardView::handleThumbnailHide(QObject* obj, QEvent* event) {
    if (event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = dynamic_cast<QMouseEvent *>(event);
        QWidget *clickedWidget = QApplication::widgetAt(mouseEvent->globalPosition().toPoint());

        // 如果点击缩略图的外面，且缩略图在展示状态，则隐藏缩略图
        bool isClickInside = false;
        if (m_cefViews.value("缩略图") && clickedWidget) {
            // 检查点击对象或父级是否是触发按钮
            QWidget *checkWidget = clickedWidget;
            while (checkWidget) {
                if (checkWidget == m_cefViews.value("缩略图")) {
                    isClickInside = true;
                    break;
                }
                checkWidget = checkWidget->parentWidget();
            }
        }
        bool hasHide = false;
        if (!isClickInside) {
            CefViewWidget *existingView = m_cefViews.value("缩略图", nullptr);
            if (existingView) {
                if (!existingView->isHidden()) {
                    existingView->hide();
                    hasHide = true;
                }
            }
        }
        // 如果点击的是缩略图，且缩略图在展示状态，则隐藏缩略图
        if (m_bottomBarWidget->isThumbnailButton(clickedWidget)) {
            if (hasHide) {
                m_cefViews.value("缩略图", nullptr)->setProperty("hiddenByOutside", true);
                return false;
            }
        }
        // 返回false，让事件继续传播
        if (hasHide) {
            return false;
        }
    }
    return QVariant();
}

void WhiteboardView::onToolManagerToolChanged(ToolType toolType)
{
    emit currentToolChanged(toolType);
}

void WhiteboardView::onFloatMenuToolSelected(const QString& toolName)
{
    qDebug() << QString("WhiteboardView: 浮动菜单选择工具: %1").arg(toolName);

    // 根据工具名称设置对应的工具
    if (toolName == "pen") {
        setCurrentTool(ToolType::FreeDraw);
    } else if (toolName == "eraser") {
        setCurrentTool(ToolType::Eraser);
        // 橡皮擦工具激活时，同步floatmenu的当前大小
        syncEraserSizeFromFloatMenu();
    } else if (toolName == "passThrough") {
        // select工具对应穿透功能
        setCurrentTool(ToolType::PassThrough);
    } else if (toolName.startsWith("graphic_")) {
        // 处理图形工具选择
        QString toolTypeStr = toolName.mid(8); // 移除 "graphic_" 前缀
        bool ok;
        int toolType = toolTypeStr.toInt(&ok);

        if (ok) {
            // 根据图形工具类型映射到对应的ToolType
            switch (toolType) {
                case 0: setCurrentTool(ToolType::Line); break;
                case 1: setCurrentTool(ToolType::DashedLine); break;
                case 2: setCurrentTool(ToolType::Arrow); break;
                case 3: setCurrentTool(ToolType::Circle); break;
                case 4: setCurrentTool(ToolType::Ellipse); break;
                case 5: setCurrentTool(ToolType::Rectangle); break;
                case 6: setCurrentTool(ToolType::Square); break;
                case 7: setCurrentTool(ToolType::Triangle); break;
                case 8: setCurrentTool(ToolType::RightTriangle); break;
                case -1:
                    // graph菜单没选择图形时，才是选择功能
                    setCurrentTool(ToolType::Lasso);
                    break;
                default:
                    setCurrentTool(ToolType::Lasso);
                    break;
            }
        }
    }
}

void WhiteboardView::onFloatMenuColorChanged(const QColor& color)
{
    setToolColor(color);
}

void WhiteboardView::onFloatMenuStrokeWidthChanged(qreal width)
{
    setToolStrokeWidth(width);
}

void WhiteboardView::onCommandSystemStateChanged()
{
    emit undoRedoStateChanged(canUndo(), canRedo());
}

void WhiteboardView::initializeWebview()
{
    // 将前端静态目录挂在自定义域名
    QString webviewPath = QCoreApplication::applicationDirPath() + "/webview";
    QCefContext::instance()->addLocalFolderResource(webviewPath, "http://domainname");

    QCefContext::instance()->addLocalFolderResource(QDir::tempPath(), "http://tempfileserver");
}

void WhiteboardView::initializeZmq() {
    // 开始上课处理
    ZmqServer::instance()->registerHandler("beginclass", [=](const ZmqMsg &request, ZmqResponseCallback callback) {
        qDebug() << "Received request: " << request.getId() << request.getMethod() << request.getData().dump();
        ClassroomInfo info;
        nlohmann::json data = request.getData();
        auto saasSchoolId = request.getData()["saasSchoolId"];
        auto saasCampusId = request.getData()["saasCampusId"];
        auto saasClassId = request.getData()["saasClassId"];
        auto saasClassName = request.getData()["saasClassName"];
        auto saasUserId = request.getData()["saasUserId"];
        auto userName = request.getData()["userName"];
        auto saasSubjectId = request.getData()["saasSubjectId"];
        auto saasSubjectCode = request.getData()["saasSubjectCode"];
        auto saasSubjectName = request.getData()["saasSubjectName"];
        auto rightToolsConfig = request.getData()["rightToolsConfig"];
        auto toolboxConfig = request.getData()["toolboxConfig"]; 
        if (saasSchoolId.is_string()) {
            info.saasSchoolId = saasSchoolId.get<std::string>().c_str();
        }
        if (saasCampusId.is_string()) {
            info.saasCampusId = saasCampusId.get<std::string>().c_str();
        }
        if (saasClassId.is_string()) {
            info.saasClassId = saasClassId.get<std::string>().c_str();
        }
        if (saasClassName.is_string()) {
            info.saasClassName = saasClassName.get<std::string>().c_str();
        }
        if (saasUserId.is_string()) {
            info.saasUserId = saasUserId.get<std::string>().c_str();
        }
        if (userName.is_string()) {
            info.userName = userName.get<std::string>().c_str();
        }
        if (saasSubjectId.is_string()) {
            info.saasSubjectId = saasSubjectId.get<std::string>().c_str();
        }
        if (saasSubjectCode.is_string()) {
            info.saasSubjectCode = saasSubjectCode.get<std::string>().c_str();
        }
        if (saasSubjectName.is_string()) {
            info.saasSubjectName = saasSubjectName.get<std::string>().c_str();
        }

        if (!rightToolsConfig.empty() && rightToolsConfig.is_array()) {
            m_toolsConfig.clear();
            auto tools = rightToolsConfig.get<nlohmann::json>();
            // 遍历数组
            for (auto& tool : tools) {
                // 判断是否是json对象
                if (tool.empty() || !tool.is_object()) {
                    continue;
                }
                SideBarConstants::ToolInfo toolInfo = createToolInfo(tool);
                m_toolsConfig.append(toolInfo);
            }
        }

        
        if (!toolboxConfig.empty() && toolboxConfig.is_array()) {
            m_toolboxToolsConfig.clear();
            auto tools = toolboxConfig.get<nlohmann::json>();
            // 遍历数组
            for (auto& tool : tools) {
                // 判断是否是json对象
                if (tool.empty() || !tool.is_object()) {
                    continue;
                }
                SideBarConstants::ToolInfo toolInfo = createToolInfo(tool);
                m_toolboxToolsConfig.append(toolInfo);
            }
        } 
        
        updateToolsConfig();

        classroomModel.setInfo(info);
        ZmqMsg response = ZmqMsg::newResponse(request,
                        nlohmann::json{
                            {"message", "开始上课初始化完成"}
                        });
        callback(response);
    });
    // 创建cef
    ZmqServer::instance()->registerHandler("createCef", [this](const ZmqMsg& request, ZmqResponseCallback callback) {
        qDebug() << "收到创建cef消息";
        SideBarConstants::ToolInfo toolInfo = createToolInfo(request.getData());
        auto update = [this, toolInfo]() {
            handleCefViewWithConfig(toolInfo);
        };
        if (QApplication::instance()->thread() == QThread::currentThread()) {
            update();
        } else {
            QMetaObject::invokeMethod(QApplication::instance(), update, Qt::QueuedConnection);
        }

        ZmqMsg response = ZmqMsg::newResponse(request,
                        nlohmann::json{
                            {"message", "创建cef成功"}
                        });
        callback(response);
    });

    // 更新资源状态
    ZmqServer::instance()->registerHandler("qt.updateResourceStatus", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        nlohmann::json data = request.getData();
        if (data.is_object()) {
            auto resourceId = data["resourceId"];
            auto documentId = data["documentId"];
            auto slide = data["slide"];
            auto total = data["total"];
            auto resourceType = data["resourceType"];
            auto pageSlide = data["pageSlide"];
            if (slide.is_number_integer() && total.is_number_integer()) {
                m_bottomBarWidget->setPageInfo(slide.get<int>(), total.get<int>());
            }
            if (pageSlide.is_number() && resourceId.is_string()) {
                toggleResourceTrace(QString("%1_%2").arg(resourceId.get<std::string>()).arg(pageSlide.get<int>()));
            }
            ResourceItem currentResource;
            currentResource.documentId = documentId.get<std::string>().c_str();
            currentResource.resourceId = resourceId.get<std::string>().c_str();
            currentResource.resourceType = resourceType.get<std::string>().c_str();
            classroomModel.setCurrentResource(currentResource);
            auto update = [this, resourceType]() {
                m_bottomBarWidget->setWhiteboardMode(resourceType == "whiteboard");
            };
            if (QApplication::instance()->thread() == QThread::currentThread()) {
                update();
            } else {
                QMetaObject::invokeMethod(QApplication::instance(), update, Qt::QueuedConnection);
            }
        }
        callback(ZmqMsg::newResponse(request, json()));
    });

    // 插入图片
    ZmqServer::instance()->registerHandler("qt.insertImageWhiteboard", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        qDebug() << "白板插入图片";
        nlohmann::json data = request.getData();
        insertImageWhiteboard(data);
        switchToDrawSelectionMode();
        nlohmann::json j = {
            {"message", "完成插入图片"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 打开资源弹窗
    ZmqServer::instance()->registerHandler("qt.openResourceDialog", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        safeOpenToolWidget("资源");
        nlohmann::json j = {
            {"message", "资源打开成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 切换到课件选择模式
    ZmqServer::instance()->registerHandler("qt.activeResourceSelection", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        switchToCoursewareSelectionMode();
        nlohmann::json j = {
            {"message", "切换课件选择模式成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 切换到套索选择模式
    ZmqServer::instance()->registerHandler("qt.activeDrawSelection", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        switchToDrawSelectionMode();
        nlohmann::json j = {
            {"message", "切换绘制选择模式成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 打开课堂小结弹窗
    ZmqServer::instance()->registerHandler("qt.openClassSummaryDialog", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        openClassSummaryDialog();
        nlohmann::json j = {
            {"message", "课堂小结打开成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 显示AI小结章鱼
    ZmqServer::instance()->registerHandler("qt.showAISummaryOctopus", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        showAISummaryOctopus();
        nlohmann::json j = {
            {"message", "显示AI小结章鱼成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 关闭AI小结章鱼
    ZmqServer::instance()->registerHandler("qt.closeAISummaryOctopus", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        closeAISummaryOctopus();
        nlohmann::json j = {
            {"message", "关闭AI小结章鱼成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 开始旋转AI小结章鱼
    ZmqServer::instance()->registerHandler("qt.startRotateAISummaryOctopus", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        startRotateAISummaryOctopus();
        nlohmann::json j = {
            {"message", "开始旋转AI小结章鱼成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 停止旋转AI小结章鱼
    ZmqServer::instance()->registerHandler("qt.stopRotateAISummaryOctopus", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        stopRotateAISummaryOctopus();
        nlohmann::json j = {
            {"message", "停止旋转AI小结章鱼成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });

    // 显示章鱼消息
    ZmqServer::instance()->registerHandler("qt.showAISummaryMsg", [=](const ZmqMsg &request, ZmqResponseCallback callback){
        nlohmann::json data = request.getData();
        if (data.is_object()) {
            auto message = data["message"];
            auto duration = data["duration"];
            if (message.is_string() && duration.is_number_integer()) {
                showAISummaryMsg(message.get<std::string>().c_str(), duration.get<int>());
            } else {
                qDebug() << "章鱼消息格式错误";
            }
        }
        nlohmann::json j = {
            {"message", "显示章鱼消息成功"}
        };
        callback(ZmqMsg::newResponse(request, j));
    });
}

void WhiteboardView::showAISummaryMsg(const QString &message, int duration) {
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this, message, duration]() {
            showAISummaryMsg(message, duration);
        }, Qt::QueuedConnection);
        return;
    }
    if (m_octopusDiscWidget) {
        m_octopusDiscWidget->showMessage(message, duration);
    }
}

void WhiteboardView::showAISummaryOctopus() {
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            showAISummaryOctopus();
        }, Qt::QueuedConnection);
        return;
    }
    createOctopusDisc();
    if (m_octopusDiscWidget) {
        connect(m_octopusDiscWidget, &OctopusDiscWidget::clicked, [this]() {
            nlohmann::json j = {
                {"rendererMethod", "classroom.getSummaryStatus"}
            };
            ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [=](const ZmqMsg &response) {
                qDebug() << "获取章鱼状态";
                nlohmann::json data = response.getData();
                if (!data.is_object()) {
                    return;
                }
                auto isSummaryLock = data["isSummaryLock"];
                auto isOpenSuccess = data["isOpenSuccess"];
                auto isOpenAISummary = data["isOpenAISummary"];
                bool _isSummaryLock = false;
                bool _isOpenSuccess = false;
                std::string _isOpenAISummary = "0";
                if (isSummaryLock.is_boolean()) {
                    _isSummaryLock = isSummaryLock.get<bool>();
                }
                if (isOpenSuccess.is_boolean()) {
                    _isOpenSuccess = isOpenSuccess.get<bool>();
                }
                if (isOpenAISummary.is_string()) {
                    _isOpenAISummary = isOpenAISummary.get<std::string>();
                }
                // 未开启课堂小结，则打开设置弹窗
                if (_isOpenAISummary != "1") {
                    openNotOpenOctopusDialog();
                    qDebug() << "未开启课堂小结，则打开设置弹窗";
                    return;
                }
                // 小章鱼正在启动中
                if (_isSummaryLock == true) {
                    qDebug() << "小章鱼正在启动中";
                    return;
                }
                // 小章鱼启动状态
                if (_isOpenSuccess == false) {
                    // 提示用户
                    showAISummaryMsg("小章鱼出错了", 3000);
                    qDebug() << "小章鱼出错了";
                    return;
                }
                // 打开小结弹窗
                openClassSummaryDialog();
                qDebug() << "打开小结弹窗";
            });
        });
    }
}

void WhiteboardView::closeAISummaryOctopus() {
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            closeAISummaryOctopus();
        }, Qt::QueuedConnection);
        return;
    }
    if (m_octopusDiscWidget) {
        destroyOctopusDisc();
    }
}

void WhiteboardView::startRotateAISummaryOctopus() {
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            startRotateAISummaryOctopus();
        }, Qt::QueuedConnection);
        return;
    }
    if (m_octopusDiscWidget) {
        m_octopusDiscWidget->startRotationAnimation();
    }
}

void WhiteboardView::stopRotateAISummaryOctopus() {
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            stopRotateAISummaryOctopus();
        }, Qt::QueuedConnection);
        return;
    }
    if (m_octopusDiscWidget) {
        m_octopusDiscWidget->stopRotationAnimation();
    }
}


void WhiteboardView::openClassSummaryDialog()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            openClassSummaryDialog();
        }, Qt::QueuedConnection);
        return;
    }
    CefViewWidget* existingView = m_cefViews.value("课堂小结", nullptr);
    if (existingView) {
        if (existingView->isVisible()) {
            qDebug() << "课堂小结窗口已经打开";
            return;
        }
    }
    handleCefViewWithConfig(WhiteboardToolConfigManager::getClassSummaryDialogConfig());
}

void WhiteboardView::initializeBridge()
{
    JSBridge::registerAsyncHandler("transComplete", [this](const JSBridgeContext &con) {
        qDebug() << "转码成功提示";
        m_sideBarWidget->showResourceButtonToolTip("报告老师，资源转码完成了！");
    });
    JSBridge::registerHandler("hideAllToolbars", [this](const JSBridgeContext &con) {
        qDebug() << "隐藏所有工具栏";
        hideSideBarWidget();
        hideBottomBarWidget();
    });
    JSBridge::registerHandler("showAllToolbars", [this](const JSBridgeContext &con) {
        qDebug() << "显示所有工具栏";
        showSideBarWidget();
        showBottomBarWidget();
    });
    JSBridge::registerHandler("qt.getCurrentResourceTraceList", [this](const JSBridgeContext &con) {
        qDebug() << "获取当前资源有轨迹列表";
        json data = con.getData().getData();
        if (data.is_object()) {
            auto resourceId = data["resourceId"];
            if (resourceId.is_string()) {
                QStringList keyLists = getTraceResourceKeys(resourceId.get<std::string>().c_str());
                std::vector<std::string> keys;
                for (const QString &key : keyLists) {
                    keys.push_back(key.toStdString());
                }
                json data = {
                    {"keys", keys}
                };
                con.setResult(JSBridgeMsg::newResponse(con.getData(), data));
                return;
            }
        }
        con.setResult(JSBridgeMsg::newResponse(con.getData(), json({
            {"keys", nlohmann::json::array()}
        })));
    });
    JSBridge::registerHandler("qt.getTraceImageList", [this](const JSBridgeContext &con) {
        qDebug() << "获取当前资源有轨迹列表";

        json data = con.getData().getData();
        QJsonArray saveImageList;
        QString fileDir = QDir::tempPath() + "/";
        if (data.is_array()) {
            for (auto item: data) {
                if (item.is_object()) {
                    auto index = item["index"];
                    auto filename = item["filename"];
                    QString qFileName = filename.get<std::string>().c_str();
                    if (index.is_string() && filename.is_string()) {
                        QJsonObject obj;
                        obj.insert("index", index.get<std::string>().c_str());
                        obj.insert("imagePath", fileDir + qFileName);
                        obj.insert("imageUrl",  "http://tempfileserver/" + qFileName),
                        saveImageList.append(obj);
                    }
                }
            }
        }
        QJsonArray res = saveResourceImages(saveImageList);
        nlohmann::json arr = nlohmann::json::array();
        for (QJsonValue item : res) {
            if (item.isObject()) {
                QJsonObject obj = item.toObject();
                QString index = obj.value("index").toString();
                QString imagePath = obj.value("imagePath").toString();
                QString imageUrl = obj.value("imageUrl").toString();
                QJsonValue isSuccess = obj.value("isSuccess").toBool();
                arr.push_back(nlohmann::json({
                    {"index", index.toStdString()},
                    {"imagePath", imagePath.toStdString()},
                    {"imageUrl", imageUrl.toStdString()},
                    {"isSuccess", isSuccess.toBool()}
                }));
            }
        }
        con.setResult(JSBridgeMsg::newResponse(con.getData(), arr));
    });

    // 退出上课页面
    JSBridge::registerHandler("classroom.exit", [this](const JSBridgeContext &con) {
        exitClassroom();
    });

    // 打开设置弹窗
    JSBridge::registerHandler("qt.openClassroomSettingDialog", [this](const JSBridgeContext &con) {
        WhiteboardView::openSettingDialog();
    });
}

void WhiteboardView::openSettingDialog()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            openSettingDialog();
        }, Qt::QueuedConnection);
        return;
    }
    handleCefViewWithConfig(WhiteboardToolConfigManager::getSettingDialogConfig());
}

void WhiteboardView::openNotOpenOctopusDialog()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            openNotOpenOctopusDialog();
        }, Qt::QueuedConnection);
        return;
    }
    handleCefViewWithConfig(WhiteboardToolConfigManager::getNotOpenOctopusDialogConfig());
}

void WhiteboardView::exitClassroom()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            exitClassroom();
        }, Qt::QueuedConnection);
        return;
    }

    // 关闭当前所有打开的小组件（安全遍历）
    QMap<QString, CefViewWidget*> cefViewsCopy = m_cefViews;
    for (auto it = cefViewsCopy.begin(); it != cefViewsCopy.end(); ++it) {
        CefViewWidget* widget = it.value();
        if (widget) {
            widget->onCloseButtonClicked();
        }
    }
    // 通知上课页面返回到桌面
    nlohmann::json j = {
        {"rendererMethod", "classroom.exit"}
    };
    ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [=](const ZmqMsg &response) {
        qDebug() << "classroom.exit 退出成功";
    });

    // 隐藏Qt应用窗口
    hideQtApplication();
}

/**
 * @brief 隐藏Qt应用窗口
 */
void WhiteboardView::hideQtApplication()
{
    WindowUtils::minimizeAllWindow();
}

void WhiteboardView::switchToCoursewareSelectionMode()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            switchToCoursewareSelectionMode();
        }, Qt::QueuedConnection);
        return;
    }
    if (m_floatMenuWidget) {
        setCurrentTool(ToolType::PassThrough);
        m_floatMenuWidget->setSelectedTool("passThrough");
    }
}

void WhiteboardView::switchToDrawSelectionMode()
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this]() {
            switchToDrawSelectionMode();
        }, Qt::QueuedConnection);
        return;
    }
    if (m_floatMenuWidget) {
        setCurrentTool(ToolType::Lasso);
        m_floatMenuWidget->setSelectedTool("");
    }
}

void WhiteboardView::setupWindowTransparency()
{
    // WhiteboardView作为MainWindow的子控件，不需要设置窗口标志
    // 只设置透明背景属性
    setAttribute(Qt::WA_TranslucentBackground);

    // 设置窗口背景为完全透明，让画布控制背景显示
    setStyleSheet("QWidget#WhiteboardView { background-color: transparent; }");

    qDebug() << "WhiteboardView: 透明属性设置完成";
}

void WhiteboardView::initializeFloatMenu()
{
    // 创建浮动菜单组件，传递主窗口作为父窗口以建立正确的层级关系
    QWidget* mainWindow = window(); // 获取顶级窗口
    m_floatMenuWidget = new FloatMenuWidget(mainWindow);
    m_floatMenuWidget->setObjectName("FloatMenuWidget");

    // 设置主窗口引用（用于定位）
    m_floatMenuWidget->setMainWindow(this);

    if (m_floatMenuWidget) {
        m_floatMenuWidget->showFloatingWindow();
        qDebug() << "WhiteboardView: 立即显示浮动菜单";
    }

    qDebug() << "WhiteboardView 浮动菜单初始化完成（子窗口模式）";
}



void WhiteboardView::initializeSideBar()
{
    // 创建侧边栏组件，传递主窗口作为父窗口以建立正确的层级关系
    QWidget* mainWindow = window(); // 获取顶级窗口
    m_sideBarWidget = new SideBarWidget(mainWindow);
    m_sideBarWidget->setObjectName("SideBarWidget");

    // 设置主窗口引用（用于定位）
    m_sideBarWidget->setMainWindow(this);

    // 连接侧边栏信号
    connect(m_sideBarWidget, &SideBarWidget::expandedChanged, [this](bool expanded) {
        qDebug() << QString("WhiteboardView: 侧边栏状态改变: %1").arg(expanded ? "展开" : "收起");
    });

    connect(m_sideBarWidget, &SideBarWidget::collapseRequested, [this]() {
        qDebug() << "WhiteboardView: 侧边栏收起请求";
    });

    // 连接按钮点击信号
    connect(m_sideBarWidget, &SideBarWidget::buttonClicked,
            this, &WhiteboardView::onSideBarButtonClicked);

    // 初始化工具配置
    m_toolsConfig = createSideBarToolsConfig();
    updateToolsConfig();

    if (m_sideBarWidget) {
        m_sideBarWidget->showIndependentWindow();

        qDebug() << "WhiteboardView: 注册初始化监听器";
        ZmqServer::instance()->registerInitListener([this](const std::string &identity) {
            qDebug() << "WhiteboardView: 客户端连接已建立，打开资源窗口, identity: " << identity.c_str();
            QMetaObject::invokeMethod(this, [this]() {
                if (m_sideBarWidget) {
                    m_sideBarWidget->openWidget("资源");
                }
            }, Qt::QueuedConnection);

            // 移除监听器
            ZmqServer::instance()->removeInitListener();
        });
    }

    qDebug() << "WhiteboardView: 侧边栏初始化完成（独立窗口模式）";
}

void WhiteboardView::initializeBottomBar()
{
    // 创建底部操作栏组件，传递主窗口作为父窗口以建立正确的层级关系
    QWidget* mainWindow = window(); // 获取顶级窗口
    m_bottomBarWidget = new BottomBarWidget(mainWindow);
    m_bottomBarWidget->setObjectName("BottomBarWidget");

    // 设置主窗口引用（用于定位）
    m_bottomBarWidget->setMainWindow(this);

    // 连接初始化完成信号
    connect(m_bottomBarWidget, &BottomBarWidget::initializationCompleted, [this]() {
        qDebug() << "WhiteboardView: 底部操作栏初始化完成";
        // 设置初始页码信息
        if (m_bottomBarWidget) {
            m_bottomBarWidget->setPageInfo(1, 1);
        }
    });

    // 连接按钮点击信号
    connect(m_bottomBarWidget, &BottomBarWidget::prevPPTPageClicked, [this]() {
        qDebug() << "WhiteboardView: 上一页按钮点击";
        nlohmann::json j = {
            {"rendererMethod", "classroom.prevFrame"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "切换上一页触发成功";
        });
    });

    connect(m_bottomBarWidget, &BottomBarWidget::nextPPTPageClicked, [this]() {
        qDebug() << "WhiteboardView: 下一页按钮点击";
        nlohmann::json j = {
            {"rendererMethod", "classroom.nextFrame"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "切换下一页触发成功";
        });
    });

    connect(m_bottomBarWidget, &BottomBarWidget::whiteboardClicked, [this]() {
        qDebug() << "WhiteboardView: 白板按钮点击";
        nlohmann::json j = {
            {"rendererMethod", "classroom.addBoard"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "添加白板触发成功";
        });
    });

    connect(m_bottomBarWidget, &BottomBarWidget::exitClicked, [this]() {
        qDebug() << "WhiteboardView: 关闭白板或课件";
        nlohmann::json j = {
            {"rendererMethod", "classroom.closeResource"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "关闭白板或课件触发成功";
        });
    });


    connect(m_bottomBarWidget, &BottomBarWidget::prevPageClicked, [this]() {
        qDebug() << "WhiteboardView: 切换上个资源按钮点击";
        nlohmann::json j = {
            {"rendererMethod", "classroom.prevResource"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "切换上个资源触发成功";
        });
    });

    connect(m_bottomBarWidget, &BottomBarWidget::nextPageClicked, [this]() {
        qDebug() << "WhiteboardView: 切换下个资源按钮点击";
        nlohmann::json j = {
            {"rendererMethod", "classroom.nextResource"}
        };
        ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
            qDebug() << "切换下个资源触发成功";
        });
    });

    connect(m_bottomBarWidget, &BottomBarWidget::thumbnailClicked, [this]() {
        qDebug() << "WhiteboardView: 缩略图按钮点击";
        // 检查是否已存在该工具的实例
        CefViewWidget* existingView = m_cefViews.value("缩略图", nullptr);
        if (existingView) {
            if (existingView->property("hiddenByOutside").toBool()) {
                qDebug() << "WhiteboardView: 缩略图已隐藏，并且通过点击缩略图按钮隐藏，不显示";
                existingView->setProperty("hiddenByOutside", false);
                return;
            }
            if (existingView->isHidden()) {
                qDebug() << "WhiteboardView: 显示缩略图";
                existingView->show();
                // 待填充参数
                nlohmann::json j = {};
                JSBridge::callJs("缩略图", JSBridgeMsg::newRequest("init", j), [](const JSBridgeContext &result) {
                    qDebug() << "callJs response: " << result.getData().serialize();
                });
            } else {
                qDebug() << "WhiteboardView: 隐藏缩略图";
                existingView->hide();
            }
        } else {
            SideBarConstants::ToolInfo thumbnailList = WhiteboardToolConfigManager::getThumbnailListConfig();
            handleCefViewWithConfig(thumbnailList);
        }
    });


    if (m_bottomBarWidget) {
        m_bottomBarWidget->showIndependentWindow();
        qDebug() << "WhiteboardView: 立即显示底部操作栏";
    }

    qDebug() << "WhiteboardView: 底部操作栏初始化完成（独立窗口模式）";
}

void WhiteboardView::createOctopusDisc()
{
    if (m_octopusDiscWidget) {
        delete m_octopusDiscWidget.get();
    }
    // 创建底部操作栏组件，传递主窗口作为父窗口以建立正确的层级关系
    QWidget* mainWindow = window(); // 获取顶级窗口
    m_octopusDiscWidget = new OctopusDiscWidget(mainWindow, m_zIndexManager);
    m_octopusDiscWidget->setObjectName("octopusDisc");

    // 距离右下角190， 30的位置
    QSize screenSize = ScreenUtils::getScreenSize();
    int x = screenSize.width() - m_octopusDiscWidget->width() - ScreenAdaptationConstants::adaptSize(30);
    int y = screenSize.height() - m_octopusDiscWidget->height() - ScreenAdaptationConstants::adaptSize(190);
    m_octopusDiscWidget->move(x, y);
    m_octopusDiscWidget->show();

    if (m_zIndexManager) {
        m_zIndexManager->registerComponent(m_octopusDiscWidget, ZIndexLevel::QT_SIDEBAR_PANEL,
                                         ComponentType::SIDEBAR, "OctopusDiscWidget");
    }
}

void WhiteboardView::destroyOctopusDisc() {
    if (m_octopusDiscWidget) {
        delete m_octopusDiscWidget.get();
    }
}

void WhiteboardView::connectSignalsAndSlots()
{

    if (m_whiteBoard) {
        connect(m_whiteBoard, &WhiteBoard::operationChanged,
        this, [this](const QString& operationType, const QString& description) {
            qDebug() << "WhiteboardView: operationChanged";
            nlohmann::json j = {
                {"rendererMethod", "qt.resourceTraceChange"}
            };
            ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [](const ZmqMsg &response) {
                qDebug() << "WhiteboardView: operationChanged 通知成功";
            });
        });
    }

    // 连接浮动菜单信号
    if (m_floatMenuWidget) {
        // 工具选择信号
        connect(m_floatMenuWidget, &FloatMenuWidget::toolSelected,
                this, &WhiteboardView::onFloatMenuToolSelected);

        // 工具类型改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::toolTypeChanged,
                this, &WhiteboardView::setCurrentTool);


        // 颜色改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::penColorChanged,
                this, &WhiteboardView::onFloatMenuColorChanged);

        // 线宽改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::penWidthChanged,
                this, &WhiteboardView::onFloatMenuStrokeWidthChanged);

        // 保存板书信号
        connect(m_floatMenuWidget, &FloatMenuWidget::saveBoard,
                this, &WhiteboardView::saveBoard);
        
        // 撤销信号
        connect(m_floatMenuWidget, &FloatMenuWidget::undoAction,
                this, &WhiteboardView::undo);

        // 菜单状态改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::menuToggled,
                this, [this](bool expanded) {
                    emit floatMenuVisibilityChanged(expanded);
                });

        // 展开状态改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::expandedChanged,
                this, [this](bool expanded) {
                    emit floatMenuVisibilityChanged(expanded);
                });

        // 浮动菜单点击信号
        connect(m_floatMenuWidget, &FloatMenuWidget::floatMenuClicked,
                this, [this]() {
                });

        // 浮动菜单双击信号
        connect(m_floatMenuWidget, &FloatMenuWidget::floatMenuDoubleClicked,
                this, [this]() {
                });

        // 位置改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::positionChanged,
                this, [this](const QPoint& newPosition) {
                });

        // 橡皮擦大小改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::eraserSizeChanged,
                this, [this](const QSizeF& size) {
                    qDebug() << QString("WhiteboardView: 橡皮擦大小改变: %1x%2").arg(size.width()).arg(size.height());
                    if (m_whiteBoard) {
                        m_whiteBoard->setEraserSize(size);
                    }
                });

        // 图形工具类型改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::graphicToolTypeChanged,
                this, [this](int type) {
                    qDebug() << QString("WhiteboardView: 图形工具类型改变: %1").arg(type);
                    // 处理图形工具选择
                    handleGraphicToolSelection(type);
                });

        // 画笔类型改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::penTypeChanged,
                this, [this](int type) {
                    qDebug() << QString("WhiteboardView: 画笔类型改变: %1").arg(type);
                    setPenType(type);
                });

        // 选中工具改变信号
        connect(m_floatMenuWidget, &FloatMenuWidget::selectedToolChanged,
                this, [this](const QString& toolName) {
                    qDebug() << QString("WhiteboardView: 选中工具改变: %1").arg(toolName);
                });

        // 工具取消选中信号
        connect(m_floatMenuWidget, &FloatMenuWidget::toolDeselected,
                this, [this](const QString& toolName) {
                    qDebug() << QString("WhiteboardView: 工具取消选中: %1").arg(toolName);
                    // 工具取消选中时，禁用鼠标穿透
                    disableMousePassThrough();
                    // 发送Lasso工具类型信号给MainWindow（默认选择工具）
                    emit currentToolChanged(ToolType::Lasso);
                });

        // 工具滑动清屏
        connect(m_floatMenuWidget, &FloatMenuWidget::clearCanvas,
                this, [this]() {
                    qDebug() << "WhiteboardView: 滑动清屏";
                    clearCanvas();
                });
    }

    // // 连接命令系统信号
    // CommandSystem* commandSystem = CommandSystem::instance();
    // if (commandSystem) {
    //     connect(commandSystem, &CommandSystem::canUndoChanged,
    //             this, &WhiteboardView::onCommandSystemStateChanged);
    //     connect(commandSystem, &CommandSystem::canRedoChanged,
    //             this, &WhiteboardView::onCommandSystemStateChanged);

    //     // 连接重绘信号到画布视图
    //     if (m_canvasView) {
    //         connect(commandSystem, &CommandSystem::needsRepaint,
    //                 m_canvasView, QOverload<>::of(&CanvasView::update));
    //     }
    // }





    qDebug() << "WhiteboardView 信号连接完成";
}



void WhiteboardView::initializeUILayerManagement()
{
    // 设置组件Z-Order层级
    setupComponentZOrder();

    qDebug() << "WhiteboardView: UI层级管理初始化完成（简化版，无定时器）";
}

void WhiteboardView::forceCanvasToBottom()
{
    // 强制画布保持在最底层的专用方法
    if (m_whiteBoard) {
        auto* whiteBoardView = m_whiteBoard->getView();
        if (whiteBoardView) {
            whiteBoardView->lower();
            whiteBoardView->stackUnder(this);
        }

        // 立即触发UI组件置顶，确保层级正确
        ensureUIComponentsOnTop();
    }
}

void WhiteboardView::setupComponentZOrder()
{
    if (!m_zIndexManager) {
        return;
    }

    static bool isFirstCall = true;

    if (isFirstCall) {
        // 首次调用时注册所有组件到ZIndexManager，现在组件立即显示，无需延迟调用
        registerAllComponentsToZIndexManager();
        isFirstCall = false;
    }

    // 应用Z-Order
    m_zIndexManager->applyZOrder();

    qDebug() << "WhiteboardView: 通过ZIndexManager应用组件Z-Order";
}

void WhiteboardView::forceReapplyComponentZOrder()
{
    if (!m_zIndexManager) {
        return;
    }

    // 强制重新应用层级，不管是否是首次调用
    m_zIndexManager->forceReapplyLevels();

    qDebug() << "WhiteboardView: 强制重新应用组件Z-Order层级";
}

void WhiteboardView::registerAllComponentsToZIndexManager()
{
    if (!m_zIndexManager) {
        return;
    }

    qDebug() << "WhiteboardView: 开始注册所有组件到ZIndexManager";

    // 层级1: 画布层 (最底层)
    if (m_whiteBoard) {
        auto* whiteBoardView = m_whiteBoard->getView();
        if (whiteBoardView) {
            m_zIndexManager->registerComponent(whiteBoardView, ZIndexLevel::CANVAS_LAYER,
                                             ComponentType::CANVAS, "WhiteBoardView");
        }
    }

    // 层级2: 底部UI组件
    if (m_bottomBarWidget) {
        m_zIndexManager->registerComponent(m_bottomBarWidget, ZIndexLevel::BOTTOM_UI_LAYER,
                                         ComponentType::UI_PANEL, "BottomBarWidget");
    }
    
    // 层级3: Qt原生组件-圆盘(浮动菜单)
    if (m_floatMenuWidget) {
        m_zIndexManager->registerComponent(m_floatMenuWidget, ZIndexLevel::QT_FLOAT_MENU,
                                         ComponentType::FLOAT_MENU, "FloatMenuWidget");
    }

    // 层级4: 侧边栏组件 (高层级)
    if (m_sideBarWidget) {
        m_zIndexManager->registerComponent(m_sideBarWidget, ZIndexLevel::QT_SIDEBAR_PANEL,
                                         ComponentType::SIDEBAR, "SideBarWidget");
    }





    // 这里只需要确保已存在的CefView都已注册
    for (auto it = m_cefViews.begin(); it != m_cefViews.end(); ++it) {
        const QString& toolName = it.key();
        CefViewWidget* view = it.value();
        if (view) {
            // 检查是否已注册，如果没有则使用默认层级注册
            if (m_zIndexManager->getComponentLevel(view) == ZIndexLevel::BOTTOM_LAYER_BASE) {
                // 使用默认层级注册（因为静态配置已移除）
                ZIndexLevel level = ZIndexLevel::CEF_MIDDLE_LAYER_BASE; // 默认层级

                m_zIndexManager->registerComponent(view, level, ComponentType::CEF_WIDGET,
                                                 QString("%1View").arg(toolName));
                qDebug() << QString("WhiteboardView: 补充注册%1到层级管理器，层级: %2").arg(toolName).arg(static_cast<int>(level));
            }
        }
    }

    qDebug() << "WhiteboardView: 组件注册完成";
}

void WhiteboardView::ensureUIComponentsOnTop()
{
    // 使用ZIndexManager确保组件层级正确
    if (m_zIndexManager) {
        m_zIndexManager->applyZOrder();
        qDebug() << "WhiteboardView: 通过ZIndexManager确保UI组件在顶层";
    }
}

void WhiteboardView::updateCanvasSize()
{
    if (!m_isInitialized) {
        return;
    }

    // 获取当前窗口的实际大小，画布全屏展示
    QSize windowSize = size();
    QSize canvasSize = windowSize; // 画布占满整个窗口

    // 确保尺寸有效
    if (canvasSize.width() < 800) canvasSize.setWidth(800);
    if (canvasSize.height() < 600) canvasSize.setHeight(600);

    // 只有当尺寸真正改变时才重新初始化
    if (canvasSize != m_lastCanvasSize) {
        m_lastCanvasSize = canvasSize;

        // 更新新白板系统的场景大小
        if (m_whiteBoard) {
            QRectF sceneRect(0, 0, canvasSize.width(), canvasSize.height());
            m_whiteBoard->setSceneRect(sceneRect);

            // 同时更新视图的几何尺寸
            auto* whiteBoardView = m_whiteBoard->getView();
            if (whiteBoardView) {
                whiteBoardView->setGeometry(0, 0, canvasSize.width(), canvasSize.height());
            }
        }
    }


}


void WhiteboardView::syncFloatMenuStateToToolManager()
{
    if (!m_floatMenuWidget) {
        return;
    }

    // 同步浮动菜单状态到工具管理器
    // 这里可以添加从浮动菜单获取状态并应用到工具管理器的逻辑
    qDebug() << "WhiteboardView: 同步浮动菜单状态到工具管理器";
}

void WhiteboardView::handleGraphicToolSelection(int toolType)
{
    qDebug() << QString("WhiteboardView: 处理图形工具选择，类型: %1").arg(toolType);

    // 根据图形工具类型映射到对应的ToolType
    // GraphicToolPanel中的工具类型定义：
    // 第一排（3个）：直线(0)、虚线(1)、箭头(2)
    // 第二排（6个）：圆形(3)、椭圆(4)、矩形(5)、正方形(6)、三角形(7)、直角三角形(8)

    switch (toolType) {
        case 0: // 直线
            setCurrentTool(ToolType::Line);
            break;
        case 1: // 虚线
            setCurrentTool(ToolType::DashedLine);
            break;
        case 2: // 箭头
            setCurrentTool(ToolType::Arrow);
            break;
        case 3: // 圆形
            setCurrentTool(ToolType::Circle);
            break;
        case 4: // 椭圆
            setCurrentTool(ToolType::Ellipse);
            break;
        case 5: // 矩形
            setCurrentTool(ToolType::Rectangle);
            break;
        case 6: // 正方形
            setCurrentTool(ToolType::Square);
            break;
        case 7: // 三角形
            setCurrentTool(ToolType::Triangle);
            break;
        case 8: // 直角三角形
            setCurrentTool(ToolType::RightTriangle);
            break;
        case -1: // 取消选择，切换到选择工具
            // setCurrentTool(ToolType::Lasso);
            break;
        default:
            // 默认切换到选择工具
            // setCurrentTool(ToolType::Lasso);
            break;
    }
}

void WhiteboardView::syncEraserSizeFromFloatMenu()
{
    if (!m_floatMenuWidget) {
        return;
    }

    // 获取floatmenu的当前橡皮擦大小
    QSizeF currentSizeF = m_floatMenuWidget->getCurrentEraserSize();
    int currentSize = static_cast<int>(currentSizeF.width());

    // 获取橡皮擦工具并设置大小
    // auto eraserTool = ToolManager::instance()->getTool(ToolType::Eraser);
    // if (eraserTool) {
    //     auto eraser = qSharedPointerDynamicCast<EraserTool>(eraserTool);
    //     if (eraser) {
    //         eraser->setEraserWidth(currentSize);
    //         qDebug() << QString("WhiteboardView: 同步橡皮擦大小从floatmenu: %1").arg(currentSize);
    //     }
    // }
}

void WhiteboardView::onSideBarButtonClicked(const SideBarConstants::ToolInfo& toolInfo)
{
    qDebug() << QString("WhiteboardView: 侧边栏按钮被点击，工具名称: %1，视图类型: %2，点击类型: %3，URL: %4，位置: (%5,%6)，大小: %7x%8")
              .arg(toolInfo.toolName)
              .arg(static_cast<int>(toolInfo.viewType))
              .arg(static_cast<int>(toolInfo.clickType))
              .arg(toolInfo.url)
              .arg(toolInfo.geometry.x())
              .arg(toolInfo.geometry.y())
              .arg(toolInfo.geometry.width())
              .arg(toolInfo.geometry.height());

    SideBarConstants::ToolInfo toolInfoCopy = toolInfo;

    // 根据点击类型处理不同的逻辑
    switch (toolInfoCopy.clickType) {
        case SideBarConstants::ClickType::Qt:
            qDebug() << "WhiteboardView: Qt工具被点击: " << toolInfoCopy.toolName;
            // 处理Qt工具的逻辑
            handleQtTool(toolInfoCopy);
            break;
        case SideBarConstants::ClickType::Cef:
            qDebug() << QString("WhiteboardView: CEF工具被点击: %1").arg(toolInfo.toolName);
            handleToolWithConfig(toolInfo);
            break;

        case SideBarConstants::ClickType::ToolBox:
            qDebug() << "WhiteboardView: 工具箱按钮被点击";
            // 计算弹窗显示位置
            if (m_sideBarWidget) {
                // 获取侧边栏的全局位置
                QPoint sidebarGlobalPos = m_sideBarWidget->mapToGlobal(QPoint(0, 0));
                // 弹窗显示在侧边栏左边
                QPoint popupPos(sidebarGlobalPos.x() - SideBarConstants::getToolboxPopupWidth() - SideBarConstants::getToolboxPopupSpacing(),
                               sidebarGlobalPos.y() + 100); // 稍微向下偏移
                showToolboxPopup(popupPos);
            }
            break;

        case SideBarConstants::ClickType::ElectronFunc:
            qDebug() << QString("WhiteboardView: Electron功能被点击: %1").arg(toolInfo.toolName);
            // 处理Electron相关功能
            handleElectronFunction(toolInfo);
            break;

        case SideBarConstants::ClickType::None:
            qDebug() << QString("WhiteboardView: 无点击类型的按钮被点击: %1").arg(toolInfo.toolName);
            // 分割线等无点击功能的组件
            break;

        default:
            qDebug() << QString("WhiteboardView: 未知的点击类型: %1").arg(static_cast<int>(toolInfo.clickType));
            break;
    }
}


void WhiteboardView::handleToolWithConfig(const SideBarConstants::ToolInfo& toolInfo)
{
    qDebug() << QString("WhiteboardView: 使用配置信息处理工具: %1").arg(toolInfo.toolName);

    // 检查是否有有效的URL配置
    if (toolInfo.url.isEmpty()) {
        qDebug() << QString("WhiteboardView: 工具 %1 没有URL配置").arg(toolInfo.toolName);
        return;
    }

    // 使用统一的CefView管理方法
    handleCefViewWithConfig(toolInfo);
}

void WhiteboardView::handleCefViewWithConfig(const SideBarConstants::ToolInfo& toolInfo)
{
    const QString& toolName = toolInfo.toolName;
    const QString& url = toolInfo.url;

    if (toolName == "放大镜") {
        handleQtTool(toolInfo);
        return;
    }

    qDebug() << QString("WhiteboardView: 使用配置处理%1工具，URL: %2，位置: (%3,%4)，大小: %5x%6")
              .arg(toolName)
              .arg(url)
              .arg(toolInfo.geometry.x())
              .arg(toolInfo.geometry.y())
              .arg(toolInfo.geometry.width())
              .arg(toolInfo.geometry.height());

    // 检查是否已存在该工具的实例
    CefViewWidget* existingView = m_cefViews.value(toolName, nullptr);

    if (existingView) {
        // 获取当前实例的URL
        QString currentUrl = existingView->property("currentUrl").toString();

        if (currentUrl == url) {
            qDebug() << QString("WhiteboardView: 检测到相同URL，切换显示状态 (URL: %1)").arg(url);
            // 同一个URL，切换显示状态
            if (existingView->isVisible()) {
                existingView->hide();
                qDebug() << QString("WhiteboardView: 隐藏%1 CefViewWidget (URL: %2)").arg(toolName).arg(url);
            } else {
                existingView->show();
                if (m_zIndexManager) m_zIndexManager->applyZOrder();
                // 待填充参数
                nlohmann::json j = {};
                JSBridge::callJs(toolName, JSBridgeMsg::newRequest("show", j), [](const JSBridgeContext &result) {
                    qDebug() << "[show] callJs response: " << result.getData().serialize();
                });
                qDebug() << QString("WhiteboardView: 显示%1 CefViewWidget (URL: %2)").arg(toolName).arg(url);
            }
            return;
        } else {
            // 不同的URL，先关闭现有实例
            qDebug() << QString("WhiteboardView: 检测到不同URL，关闭现有%1实例 (当前URL: %2, 新URL: %3)")
                      .arg(toolName).arg(currentUrl).arg(url);
            existingView->hide();
            // 隐藏后重新应用Z-order
            if (m_zIndexManager) {
                m_zIndexManager->unregisterComponent(existingView);
                m_zIndexManager->applyZOrder();
            }
            existingView->deleteLater();
            m_cefViews.remove(toolName);
        }
    }

    // 创建新的CefViewWidget，传递主窗口作为父窗口以建立正确的层级关系
    QWidget* mainWindow = window(); // 获取顶级窗口
    CefViewWidget* newView = new CefViewWidget(mainWindow, toolInfo.toolName, toolInfo.showFullScreenButton, toolInfo.showControlBar, toolInfo.showDragBar);
    if (toolInfo.zoomLevel != 0) {
        newView->setZoomLevel(toolInfo.zoomLevel);
    }
    if (toolInfo.bgColor.isEmpty()) {
        newView->setBackgroundColor(Qt::transparent);
    } else {
        newView->setBackgroundColor(toolInfo.bgColor);
    }

    // 保存到容器中
    m_cefViews[toolName] = newView;

    connect(newView, &CefViewWidget::widgetShown, this, [this, toolName, newView, toolInfo]() {
        if (m_zIndexManager) {
            ZIndexLevel zLevel = static_cast<ZIndexLevel>(toolInfo.zlevel);
            m_zIndexManager->registerComponent(newView, zLevel, ComponentType::CEF_WIDGET,
                                             QString("%1View").arg(toolName));
        }
    });

    connect(newView, &CefViewWidget::widgetHidden, this, [this, toolName, newView]() {
        if (m_zIndexManager) {
            m_zIndexManager->unregisterComponent(newView);
        }
    });

    // 保存当前URL和工具名称到属性中
    newView->setProperty("currentUrl", url);
    newView->setProperty("toolName", toolName);

    // 使用配置信息设置CefViewWidget的位置和大小
    newView->setCefViewGeometry(toolInfo.geometry);

    // 配置底部栏的显示隐藏
    newView->setControlBarVisible(toolInfo.showControlBar);

    // 配置拖拽bar的显示隐藏
    newView->setDragBarVisible(toolInfo.showDragBar);

    // 使用配置中的层级信息注册到ZIndexManager
    if (m_zIndexManager) {
        ZIndexLevel zLevel = static_cast<ZIndexLevel>(toolInfo.zlevel);
        m_zIndexManager->registerComponent(newView, zLevel, ComponentType::CEF_WIDGET,
                                         QString("%1View").arg(toolName));
        qDebug() << QString("WhiteboardView: 注册%1到ZIndexManager，层级: %2").arg(toolName).arg(toolInfo.zlevel);
    }

    // 连接关闭信号
    connect(newView, &CefViewWidget::closeRequested, [this, toolName]() {
        CefViewWidget* view = m_cefViews.take(toolName);  // take()会移除并返回实例
        if (view) {
            QString currentUrl = view->property("currentUrl").toString();
            if (m_zIndexManager) {
                m_zIndexManager->unregisterComponent(view);
                qDebug() << QString("WhiteboardView: 从ZIndexManager注销 %1").arg(toolName);
            }

            view->deleteLater();
            qDebug() << QString("WhiteboardView: %1 CefViewWidget完全移除 (URL: %2)").arg(toolName).arg(currentUrl);
        }
    });

    // 显示CefViewWidget
    newView->setUrl(toolInfo.url);
    newView->show();
    if (m_zIndexManager) m_zIndexManager->applyZOrder();
    

    qDebug() << QString("WhiteboardView: 创建%1 CefViewWidget成功 (URL: %2)").arg(toolName).arg(url);
}

CefViewWidget* WhiteboardView::getCefView(const QString& toolName) const
{
    return m_cefViews.value(toolName, nullptr);
}

void WhiteboardView::enableMousePassThrough()
{
    if (!m_mousePassThroughEnabled) {
        m_mousePassThroughEnabled = true;
    }
}

void WhiteboardView::disableMousePassThrough()
{
    if (m_mousePassThroughEnabled) {
        m_mousePassThroughEnabled = false;
    }
}

SideBarConstants::ToolInfo WhiteboardView::createToolInfo(const json &config)
{
    SideBarConstants::ToolInfo info;
    if (!config.is_object()) {
        return info;
    }

    // 安全访问字段（避免不存在字段导致异常）
    // 1. 处理必填字段（前四个字段）
    if (config.contains("toolName") && config["toolName"].is_string()) {
        info.toolName = config["toolName"].get<std::string>().c_str();
    }
    if (config.contains("toolIcon") && config["toolIcon"].is_string()) {
        info.toolIcon = config["toolIcon"].get<std::string>().c_str();
    }
    if (config.contains("viewType") && config["viewType"].is_number_integer()) {
        info.viewType = config["viewType"].get<SideBarConstants::ViewType>();
    }
    if (config.contains("clickType") && config["clickType"].is_number_integer()) {
        info.clickType = config["clickType"].get<SideBarConstants::ClickType>();
    }

    // 2. 处理可选字段（其他字段可能不存在）
    if (config.contains("url") && config["url"].is_string()) {
        info.url = config["url"].get<std::string>().c_str();
    }
    if (config.contains("geometry") && config["geometry"].is_array() &&
        config["geometry"].size() >= 4)
    {
        const auto& geom = config["geometry"];
        if (geom[0].is_number() && geom[1].is_number() &&
            geom[2].is_number() && geom[3].is_number())
        {
            info.geometry = QRect(
                static_cast<int>(geom[0].get<float>()),
                static_cast<int>(geom[1].get<float>()),
                static_cast<int>(geom[2].get<float>()),
                static_cast<int>(geom[3].get<float>())
            );
        }
    }
    if (config.contains("zlevel") && config["zlevel"].is_number_integer()) {
        info.zlevel = config["zlevel"].get<int>();
    }

    // 3. 处理布尔字段（默认值false）
    info.showToolBox = false;
    if (config.contains("showToolBox") && config["showToolBox"].is_boolean()) {
        info.showToolBox = config["showToolBox"].get<bool>();
    }

    info.toolType = "";
    if (config.contains("toolType") && config["toolType"].is_string()) {
        info.toolType = config["toolType"].get<std::string>().c_str();
    }

    info.showDragBar = false; // 默认值
    if (config.contains("showDragBar") && config["showDragBar"].is_boolean()) {
        info.showDragBar = config["showDragBar"].get<bool>();
    }

    info.showControlBar = false; // 默认值
    if (config.contains("showControlBar") && config["showControlBar"].is_boolean()) {
        info.showControlBar = config["showControlBar"].get<bool>();
    }

    info.fullscreen = false; // 默认值
    if (config.contains("fullscreen") && config["fullscreen"].is_boolean()) {
        info.fullscreen = config["fullscreen"].get<bool>();
    }

    info.showFullScreenButton = false;
    if (config.contains("showFullScreenButton") && config["showFullScreenButton"].is_boolean()) {
        info.showFullScreenButton = config["showFullScreenButton"].get<bool>();
    }

    info.bgColor = "";
    if (config.contains("bgColor") && config["bgColor"].is_string()) {
        info.bgColor = config["bgColor"].get<std::string>().c_str();
    }
    info.zoomLevel = 0;
    if (config.contains("zoomLevel") && config["zoomLevel"].is_number()) {
        info.zoomLevel = config["zoomLevel"].get<double>();
    }

    return info;
}

QVector<QRect> WhiteboardView::getUIRegions() const
{
    QVector<QRect> regions;

    // 由于所有UI组件都是子控件，直接使用相对位置即可
    
    // 添加FloatMenuWidget区域
    if (m_floatMenuWidget && m_floatMenuWidget->isVisible()) {
        regions.append(QRect(m_floatMenuWidget->pos(), m_floatMenuWidget->size()));
    }





    // 添加CefViewWidget区域
    for (auto it = m_cefViews.begin(); it != m_cefViews.end(); ++it) {
        CefViewWidget* view = it.value();
        if (view && view->isVisible()) {
            regions.append(QRect(view->pos(), view->size()));
        }
    }

    return regions;
}

void WhiteboardView::showToolboxPopup(const QPoint& position)
{
    if (!m_toolboxPopup) {

        m_toolboxPopup = new ToolboxPopup(m_toolboxToolsConfig, this);

        // 连接信号
        connect(m_toolboxPopup, &ToolboxPopup::toolClicked,
                this, &WhiteboardView::onToolboxPopupToolClicked);
        connect(m_toolboxPopup, &ToolboxPopup::popupClosed,
                this, &WhiteboardView::hideToolboxPopup);

        // 注册到ZIndexManager，与侧边栏同级
        if (m_zIndexManager) {
            m_zIndexManager->registerComponent(m_toolboxPopup, ZIndexLevel::QT_SIDEBAR_PANEL,
                                             ComponentType::BUTTON_PANEL, "ToolboxPopup");
        }

    }

    m_toolboxPopup->setToolsConfig(m_toolboxToolsConfig);
    // 显示弹窗，传入全局坐标，ToolboxPopup内部会转换为相对坐标
    m_toolboxPopup->showAt(position);
}

void WhiteboardView::hideToolboxPopup()
{
    if (m_toolboxPopup) {
        m_toolboxPopup->hidePopup();
    }
}

void WhiteboardView::onToolboxPopupToolClicked(const SideBarConstants::ToolInfo& toolInfo)
{
    qDebug() << QString("WhiteboardView: 工具箱弹窗工具点击: %1").arg(toolInfo.toolName);

    // 处理工具点击，与侧边栏按钮点击使用相同的逻辑
    handleToolWithConfig(toolInfo);

    // 隐藏弹窗
    hideToolboxPopup();
}

QList<SideBarConstants::ToolInfo> WhiteboardView::createSideBarToolsConfig()
{
    QList<SideBarConstants::ToolInfo> toolsConfig;
    
    // 4. 动态工具按钮 (Normal类型)
    // 放大镜
    SideBarConstants::ToolInfo magnifierInfo;
    magnifierInfo.toolName = "放大镜";
    magnifierInfo.toolIcon = ":/images/side/side_amplifier.svg";
    magnifierInfo.viewType = SideBarConstants::ViewType::Normal;
    magnifierInfo.clickType = SideBarConstants::ClickType::Qt;
    magnifierInfo.url = "http://domainname/src/widget/Magnifier/index.html";
    QSize screenSize = ScreenUtils::getScreenSize();
    magnifierInfo.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    magnifierInfo.zlevel = static_cast<int>(ZIndexLevel::QT_MAGNIFIER);
    magnifierInfo.showToolBox = true; // 放大镜在工具箱中显示
    magnifierInfo.toolType = "科学工具";
    toolsConfig.append(magnifierInfo);
    
    // 随机点名
    SideBarConstants::ToolInfo randomCallInfo;
    randomCallInfo.toolName = "随机点名";
    randomCallInfo.toolIcon = ":/images/side/side_random.svg";
    randomCallInfo.viewType = SideBarConstants::ViewType::Normal;
    randomCallInfo.clickType = SideBarConstants::ClickType::Cef;
    randomCallInfo.url = "http://domainname/src/widget/RollCallPanel/index.html?saasSchoolId=7305931429448790016&saasClassId=7305931429448790016";
    randomCallInfo.geometry = QRect(
        ScreenAdaptationConstants::adaptSize(3314),
        ScreenAdaptationConstants::adaptSize(796.5),
        ScreenAdaptationConstants::adaptSize(632),
        ScreenAdaptationConstants::adaptSize(578)
    );
    randomCallInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_RANDOM_CALL);
    randomCallInfo.showToolBox = true; // 随机点名在工具箱中显示
    randomCallInfo.toolType = "通用工具";
    randomCallInfo.showControlBar = true;
    randomCallInfo.showDragBar = true;
    toolsConfig.append(randomCallInfo);

    // 计时器
    SideBarConstants::ToolInfo timerInfo;
    timerInfo.toolName = "计时器";
    timerInfo.toolIcon = ":/images/side/side_time.svg";
    timerInfo.viewType = SideBarConstants::ViewType::Normal;
    timerInfo.clickType = SideBarConstants::ClickType::Cef;
    timerInfo.url = "http://domainname/src/widget/TickTock/index.html";
    timerInfo.geometry = QRect(
        ScreenAdaptationConstants::adaptSize(3370),
        ScreenAdaptationConstants::adaptSize(826),
        ScreenAdaptationConstants::adaptSize(536),
        ScreenAdaptationConstants::adaptSize(519)
    );
    timerInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_TIMER);
    timerInfo.showToolBox = true; // 计时器在工具箱中显示
    timerInfo.toolType = "数学工具";
    timerInfo.showControlBar = true;
    timerInfo.showDragBar = true;
    toolsConfig.append(timerInfo);


    // // 洋葱课堂
    // SideBarConstants::ToolInfo yangcongInfo;
    // yangcongInfo.toolName = "洋葱课堂";
    // yangcongInfo.toolIcon = ":/images/side/side_yangcong.svg";
    // yangcongInfo.viewType = SideBarConstants::ViewType::Normal;
    // yangcongInfo.clickType = SideBarConstants::ClickType::Cef;
    // yangcongInfo.url = "https://school.yangcongxueyuan.com/teacher-workbench/resourceCenter/commonResource/microVideoList";
    // yangcongInfo.geometry = QRect(
    //     ScreenAdaptationConstants::adaptSize(100),
    //     ScreenAdaptationConstants::adaptSize(100),
    //     ScreenAdaptationConstants::adaptSize(2799),
    //     ScreenAdaptationConstants::adaptSize(1814)
    // );
    // yangcongInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_TIMER);
    // yangcongInfo.showToolBox = true;
    // yangcongInfo.showControlBar = true;
    // yangcongInfo.showDragBar = true;
    // timerInfo.toolType = "通用工具";
    // toolsConfig.append(yangcongInfo);


    return toolsConfig;
}

void WhiteboardView::handleElectronFunction(const SideBarConstants::ToolInfo& toolInfo)
{
    qDebug() << QString("WhiteboardView: 处理Electron功能: %1").arg(toolInfo.toolName);

    // 根据工具名称处理不同的Electron功能
    if (toolInfo.toolName == "退出") {
        // 可以发送信号给主窗口或直接调用退出方法
        ResourceItem currentResource = classroomModel.getCurrentResource();
        // 对象判空
        QStringList keyLists = getTraceResourceKeys(currentResource.resourceId);

        if (!keyLists.isEmpty()) {
            nlohmann::json j = {
                {"rendererMethod", "classroom.getCurrentSavedStatus"}
            };
            ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [=](const ZmqMsg &response) {
                json data = response.getData();
                bool isSaved = false;
                if (data.is_object()) {
                    auto saved = data["saved"];
                    if (saved.is_boolean()) {
                        isSaved = saved.get<bool>();
                    }
                }
                if (!isSaved) {
                    // 判断是否是主线程
                    if (QThread::currentThread() == qApp->thread()) {
                        handleToolWithConfig(toolInfo);
                    } else {
                        QMetaObject::invokeMethod(this, [this, toolInfo]() {
                            handleToolWithConfig(toolInfo);
                        });
                    }
                } else {
                    // 已保存过直接退出
                    exitClassroom();
                }
            });
        } else {
            // 直接退出
            // exitClassroom();
            handleToolWithConfig(WhiteboardToolConfigManager::getExitDialogConfig());
        }
    } else if (toolInfo.toolName == "桌面") {


        nlohmann::json j = {};
        ZmqServer::instance()->sendRequest(
            "black-board-client",
            ZmqMsg::newRequest("win-minimize", j),
            [=](const ZmqMsg &response) {
                qDebug() << "electron最小化处理 response: " << response.serialize();
            }
            );
    } else if (toolInfo.toolName == "WiFi") {
        nlohmann::json j = {};
        // 最小化
        ZmqServer::instance()->sendRequest(
            "black-board-client",
            ZmqMsg::newRequest("win-minimize", j),
            [=](const ZmqMsg &response) {
                qDebug() << "electron最小化处理 response: " << response.serialize();
            }
        );
        // 打开wifi
        ZmqServer::instance()->sendRequest(
            "black-board-client",
            ZmqMsg::newRequest("open-system-network", j),
            [](const ZmqMsg &response) {
                // TODO: 响应和日志处理
                std::cout << "wifi打开成功 response: " << response << std::endl;
            }
        );
    } else {
        qDebug() << QString("WhiteboardView: 未知的Electron功能: %1").arg(toolInfo.toolName);
    }
}

QList<SideBarConstants::ToolInfo> WhiteboardView::getToolboxToolsConfig()
{
    QList<SideBarConstants::ToolInfo> toolboxTools;

    // 从侧边栏工具配置中过滤出showToolBox为true的工具
    QList<SideBarConstants::ToolInfo> allToolsConfig = createSideBarToolsConfig();
    if (!m_toolsConfig.isEmpty()) {
        allToolsConfig = m_toolsConfig;
    }

    for (const auto& toolInfo : allToolsConfig) {
        if (toolInfo.showToolBox) {
            toolboxTools.append(toolInfo);
            qDebug() << QString("WhiteboardView: 工具 %1 将显示在工具箱中").arg(toolInfo.toolName);
        }
    }


    return toolboxTools;
}

void WhiteboardView::insertImageWhiteboard(const nlohmann::json & data)
{
    // 保证所有UI操作都在主线程
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this, data]() {
            insertImageWhiteboard(data);
        }, Qt::QueuedConnection);
        return;
    }
    if (!data.is_object()) {
        qDebug() << "insertImageWhiteboard: data is not object";
        return;
    }
    auto imageUrlJson = data["imageUrl"];
    if (!imageUrlJson.is_string()) {
        qDebug() << "insertImageWhiteboard: imageUrl is not string";
        return;
    }
    // 图片链接转本地地址
    QString imageUrl = QString::fromStdString(imageUrlJson.get<std::string>());
    QString imagePath = UrlCache::cacheUrl(imageUrl);
    if (imagePath.isEmpty()) {
        qDebug() << "insertImageWhiteboard: imagePath is empty";
        return;
    }
    qDebug() << "insertImageWhiteboard: " << imagePath.toStdString();
     //m_whiteBoard->insertImage(imagePath, QPointF(0,0), ScreenAdaptationConstants::adaptSize(2801.1), ScreenAdaptationConstants::adaptSize(1574.4));
    m_whiteBoard->insertImageAtCenter(imagePath, ScreenAdaptationConstants::adaptSize(2801.1), ScreenAdaptationConstants::adaptSize(1574.4));
}

void WhiteboardView::updateToolsConfig()
{
    auto update = [this]() {
        if (m_sideBarWidget) {
            m_sideBarWidget->setToolsConfig(m_toolsConfig);
        }
    };
    if (QApplication::instance()->thread() == QThread::currentThread()) {
        update();
    } else {
        QMetaObject::invokeMethod(QApplication::instance(), update, Qt::QueuedConnection);
    }
}

void WhiteboardView::showBottomBarWidget()
{
    if (QApplication::instance()->thread() != QThread::currentThread()) {
        QMetaObject::invokeMethod(this, "showBottomBarWidget", Qt::QueuedConnection);
        return;
    }
    if (m_bottomBarWidget) {
        m_bottomBarWidget->show();
        if (m_zIndexManager) m_zIndexManager->applyZOrder();
        // 使用安全的UI刷新机制
        UIRefreshHelper::instance()->safeForceRefresh(m_bottomBarWidget);
    }
}

void WhiteboardView::hideBottomBarWidget()
{
    if (QApplication::instance()->thread() != QThread::currentThread()) {
        QMetaObject::invokeMethod(this, "hideBottomBarWidget", Qt::QueuedConnection);
        return;
    }
    if (m_bottomBarWidget) {
        qDebug() << "WhiteboardView: 隐藏底部操作栏";
        m_bottomBarWidget->hide();
        // 隐藏后重新应用Z-order
        if (m_zIndexManager) m_zIndexManager->applyZOrder();
        // 使用安全的UI刷新机制
        UIRefreshHelper::instance()->safeForceRefresh(m_bottomBarWidget);
    }
}

void WhiteboardView::showSideBarWidget()
{
    if (QApplication::instance()->thread() != QThread::currentThread()) {
        QMetaObject::invokeMethod(this, "showSideBarWidget", Qt::QueuedConnection);
        return;
    }
    if (m_sideBarWidget) {
        m_sideBarWidget->show();
        if (m_zIndexManager) m_zIndexManager->applyZOrder();
        // 使用安全的UI刷新机制
        UIRefreshHelper::instance()->safeForceRefresh(m_sideBarWidget);
    }
}

void WhiteboardView::hideSideBarWidget()
{
    qDebug() << "调用 hideSideBarWidget, 当前线程:" << QThread::currentThread();
    if (QApplication::instance()->thread() != QThread::currentThread()) {
        QMetaObject::invokeMethod(this, "hideSideBarWidget", Qt::QueuedConnection);
        return;
    }
    if (m_sideBarWidget) {
        qDebug() << "m_sideBarWidget->hide()";
        m_sideBarWidget->hide();
        // 隐藏后重新应用Z-order
        if (m_zIndexManager) m_zIndexManager->applyZOrder();
        // 使用定时器延迟处理，避免阻塞当前操作
        QTimer::singleShot(0, [this]() {
            if (m_sideBarWidget) {
                m_sideBarWidget->repaint();
            }
        });
    }
}

void WhiteboardView::initializeWhiteBoard()
{
    m_whiteBoard = new WhiteBoard(this);

    m_whiteBoard->initialize();

    auto* whiteBoardView = m_whiteBoard->getView();
    if (whiteBoardView) {
        whiteBoardView->setParent(this);
        QSize screenSize = ScreenUtils::getScreenSize();
        whiteBoardView->setGeometry(0, 0, screenSize.width(), screenSize.height());
        whiteBoardView->show();

        // 确保多指绘制功能启用
        whiteBoardView->setMultiTouchEnabled(true);

        // 确保尺寸正确
        if (m_isInitialized) {
            updateCanvasSize();
        }
    }
}

void WhiteboardView::handleQtTool(const SideBarConstants::ToolInfo &toolInfo) {
    qDebug() << "WhiteboardView: 处理Qt工具: " << toolInfo.toolName;

    if (toolInfo.toolName == "放大镜") {
        if (m_magnifierFullWidget) {
            qDebug() << "放大镜已存在，无需创建";
        }
        else {
            // 确保其他工具隐藏
            qDebug() << "隐藏其他工具";
            hideSideBarWidget();
            hideBottomBarWidget();
            hideFloatMenu();

            // 使用安全的UI刷新机制，确保UI重绘完成
            UIRefreshHelper::instance()->safeProcessEvents();

            qDebug() << "创建放大镜";

            QWidget* mainWindow = window();
            m_magnifierFullWidget = new MagnifierFullWidget(mainWindow, m_zIndexManager);
            m_magnifierFullWidget->show();

            // 使用配置中的层级信息注册到ZIndexManager
            if (m_zIndexManager) {
                ZIndexLevel zLevel = static_cast<ZIndexLevel>(toolInfo.zlevel);
                m_zIndexManager->registerComponent(m_magnifierFullWidget, zLevel, ComponentType::SIDEBAR,
                                                QString("%1View").arg(toolInfo.toolName));            
            }

            // zmq通知renderer
            nlohmann::json j = {
                {"rendererMethod", "classroom.openMagnifier"}
            };
            ZmqServer::instance()->sendRequest("black-board-client", ZmqMsg::newRequest("callElectronRenderer", j), [=](const ZmqMsg &response) {
                qDebug() << "classroom.openMagnifier 打开放大镜通知成功";
            });

            connect(m_magnifierFullWidget, &MagnifierWidget::destroyed, this, [this, toolInfo]() {
                qDebug() << "关闭放大镜";
                // 显示其他工具
                showFloatMenu();
                showBottomBarWidget();
                showSideBarWidget();
                if (m_zIndexManager) {
                    m_zIndexManager->unregisterComponent(m_magnifierFullWidget);
                }
            });
        }
    }
}

void WhiteboardView::safeOpenToolWidget(const QString& toolName)
{
    // 1. 保证主线程执行
    if (QThread::currentThread() != qApp->thread()) {
        QMetaObject::invokeMethod(this, [this, toolName]() {
            safeOpenToolWidget(toolName);
        }, Qt::QueuedConnection);
        return;
    }

    qDebug() << QString("WhiteboardView: 安全打开工具窗口: %1").arg(toolName);

    // 2. 检查是否已经打开了，避免二次触发
    // 首先检查CefView是否已存在且可见
    CefViewWidget* existingView = m_cefViews.value(toolName, nullptr);
    if (existingView && existingView->isVisible()) {
        qDebug() << QString("WhiteboardView: %1工具已打开且可见，跳过重复打开").arg(toolName);
        return;
    }
    
    // 3. 通过侧边栏打开
    if (m_sideBarWidget) {
        m_sideBarWidget->openWidget(toolName);
        return;
    }
    
    qDebug() << QString("WhiteboardView: 未找到工具配置且无法通过侧边栏打开: %1").arg(toolName);
}

