#pragma once

#include <QPainterPath>
#include <QRectF>
#include <QPointF>
#include <QList>
#include <QDebug>
#include <QLineF>
#include <QTransform>

/**
 * @brief Qt路径切割器 - 基于Qt qpathclipper.cpp源码实现
 * 
 * 直接使用Qt内部的路径切割算法，实现对开放路径的矩形切割
 * 保持路径的开放性，不自动闭合路径
 */
class QtPathClipper
{
public:
    /**
     * @brief 对路径执行矩形切割，保持开放路径特性
     * @param path 输入的开放路径
     * @param clipRect 切割矩形
     * @return 切割后的路径（可能包含多个子路径）
     */
    static QPainterPath clipOpenPath(const QPainterPath& path, const QRectF& clipRect);

private:
    /**
     * @brief 将路径分解为子路径列表
     */
    static QList<QPainterPath> toSubpaths(const QPainterPath& path);

    /**
     * @brief 连续地添加路径，避免不必要的MoveToElement
     * @param target 目标路径
     * @param source 源路径
     */
    static void addPathContinuously(QPainterPath& target, const QPainterPath& source);

    /**
     * @brief 移除路径中的重复点
     * @param path 输入路径
     * @return 清理后的路径
     */
    static QPainterPath removeDuplicatePoints(const QPainterPath& path);

    /**
     * @brief 矩形边枚举
     */
    enum Edge {
        Left = 0,
        Top = 1,
        Right = 2,
        Bottom = 3
    };

    /**
     * @brief 检查边是否为垂直边
     */
    static bool isVertical(Edge edge);

    /**
     * @brief 点与阈值比较（基于边类型）
     */
    template<Edge edge>
    static bool compare(const QPointF& p, qreal t);

    /**
     * @brief 计算线段与边的交点
     */
    template<Edge edge>
    static QPointF intersectLine(const QPointF& a, const QPointF& b, qreal t);

    /**
     * @brief 添加线段到路径
     */
    static void addLine(QPainterPath& path, const QLineF& line);

    /**
     * @brief 对线段执行边切割
     */
    template<Edge edge>
    static void clipLine(const QPointF& a, const QPointF& b, qreal t, QPainterPath& result);

    /**
     * @brief 对单个子路径执行单边切割
     */
    template<Edge edge>
    static QPainterPath clip(const QPainterPath& path, qreal t);

    /**
     * @brief 路径与矩形相交切割的核心实现
     */
    static QPainterPath intersectPath(const QPainterPath& path, const QRectF& rect);

    /**
     * @brief 从路径中减去矩形区域（橡皮擦功能）
     */
    static QPainterPath subtractRect(const QPainterPath& path, const QRectF& rect);

    /**
     * @brief 将路径切割为在矩形外的部分
     */
    static QList<QPainterPath> clipPathOutsideRect(const QPainterPath& path, const QRectF& rect);

    /**
     * @brief 检查线段是否与矩形相交
     */
    static bool lineIntersectsRect(const QPointF& p1, const QPointF& p2, const QRectF& rect);

    /**
     * @brief 找到线段与矩形的交点
     */
    static QPointF findRectIntersection(const QPointF& p1, const QPointF& p2, const QRectF& rect);
};
