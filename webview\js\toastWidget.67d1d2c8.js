import { a as Bridge, l as logger } from "./index.f30fa4a1.js";
function showError(message, options = {}) {
  showToast(message, {
    ...options,
    type: "error"
  });
}
function showSuccess(message, options = {}) {
  showToast(message, {
    ...options,
    type: "success"
  });
}
function showWarning(message, options = {}) {
  showToast(message, {
    ...options,
    type: "warning"
  });
}
async function showToast(message, options = {}) {
  try {
    const { duration = 3e3, type = "info", position = "bottom" } = options;
    await Bridge.getInstance().call("toast", {
      type,
      message,
      position,
      duration
    });
  } catch (e) {
    logger.warn("【toast-widget】", "弹框显示失败", e, message);
  }
}
export {
  showError as a,
  showSuccess as b,
  showToast as c,
  showWarning as s
};
