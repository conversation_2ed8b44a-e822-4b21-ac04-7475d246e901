.text-flip[data-v-cdd7a317] {
  display: block;
  height: 100%;
  overflow: hidden;
  transition: transform 1s ease;
}
.subject-wrapper[data-v-f2adc095] {
  width: 72.66667rem;
}
.subject-tip[data-v-f2adc095] {
  font-size: 2.16667rem;
  color: #ffffff;
  line-height: 3.08333rem;
  padding-left: 3.16667rem;
}
.subject-list[data-v-f2adc095] {
  padding: 1.5rem 3.16667rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, 14.83333rem);
  justify-content: space-between;
  margin: 1.5rem 0;
  max-height: 15rem;
  overflow: auto;
  row-gap: 1.66667rem;
}
.subject-item[data-v-f2adc095] {
  width: 14.83333rem;
  height: 5.5rem;
  border-radius: 1.58333rem;
  box-sizing: border-box;
  background: #ffffff;
  border: 0 solid #979797;
  padding: 0 0.83333rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 2.16667rem;
  line-height: 5.5rem;
  text-align: center;
}
.subject-item-active[data-v-f2adc095] {
  background: #caf6df;
  color: var(--color-success);
}
.subject-bottom[data-v-f2adc095] {
  height: 12.25rem;
  background: var(--color-primary);
  border-radius: 0.33333rem 0.33333rem 3.16667rem 3.16667rem;
}
.button[data-v-f2adc095] {
  letter-spacing: 0.33333rem;
  text-indent: 0.33333rem;
  width: 15.66667rem;
}
.button[data-v-f2adc095]:not(.el-button--primary) {
  background: rgba(255, 255, 255, 0.45);
}
.button.disable[data-v-f2adc095] {
  opacity: 0.25;
}
[data-v-f2adc095] .dialog-title {
  padding-bottom: 1.58333rem;
}
.lesson-card-rest[data-v-a3d8fb46] {
  height: 19.5rem;
  border-radius: 3.16667rem;
  display: flex;
  background: linear-gradient(180deg, #f1f1fc 0%, #f1e7f9 100%);
  position: relative;
}
.lesson-card-rest-right[data-v-a3d8fb46] {
  flex: 1;
  padding-top: 3.16667rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.lesson-card-rest .lesson-assistant[data-v-a3d8fb46] {
  width: 24rem;
  height: 21.5rem;
  background: url('../webp/<EMAIL>') no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  top: 0.25rem;
  left: 16.66667rem;
}
.lesson-card-rest .lesson-start[data-v-a3d8fb46] {
  margin-top: 1.25rem;
  margin-left: 27.16667rem;
}
.lesson-card-rest .lesson-bing-class[data-v-a3d8fb46] {
  margin-top: 1.25rem;
  margin-left: 27.16667rem;
}
.lesson-status-rest[data-v-a3d8fb46] {
  height: 6rem;
  overflow: hidden;
  margin-left: 27.16667rem;
  position: relative;
}
.lesson-status-rest-bg[data-v-a3d8fb46] {
  height: 6rem;
  background: #5f52e3;
  opacity: 0.1;
  filter: blur(2.5rem);
  border-radius: 2.83333rem;
  position: absolute;
  top: 50%;
  left: 2.5rem;
  right: 2.5rem;
  margin-top: -3rem;
}
.lesson-status-rest-text[data-v-a3d8fb46] {
  height: 6rem;
  font-weight: 400;
  font-size: 2.16667rem;
  color: rgba(95, 82, 227, 0.6);
  line-height: 6rem;
  text-align: center;
  padding: 0 5rem;
}
.lesson-card[data-v-a3d8fb46] {
  height: 22.75rem;
  border-radius: 3.16667rem 3.16667rem 1rem 3.16667rem;
  display: flex;
  position: relative;
  background: url('../webp/<EMAIL>') no-repeat top center;
  background-size: 100% auto;
}
.lesson-card.is-morning-read[data-v-a3d8fb46] {
  background: url('../webp/<EMAIL>') no-repeat top center;
  background-size: 100% auto;
}
.lesson-card.is-morning-read-not-session[data-v-a3d8fb46] {
  background: url('../webp/<EMAIL>') no-repeat top center;
  background-size: 100% auto;
}
.lesson-card-center[data-v-a3d8fb46] {
  width: 32.66667rem;
  height: 9.41667rem;
  overflow: hidden;
  flex: 1;
  display: flex;
  padding-left: 38.08333rem;
  padding-top: 5rem;
}
.lesson-card-right[data-v-a3d8fb46] {
  width: 22.58333rem;
  margin-top: 6.75rem;
}
.lesson-card .lesson-assistant[data-v-a3d8fb46] {
  width: 24rem;
  height: 21.5rem;
  position: absolute;
  top: 0.25rem;
  left: 3.33333rem;
  background: url('../webp/<EMAIL>') no-repeat center center;
  background-size: 100% 100%;
}
.lesson-card .lesson-divider-wrap[data-v-a3d8fb46] {
  flex: 1;
  height: 100%;
  max-width: 15rem;
}
.lesson-card .lesson-divider[data-v-a3d8fb46] {
  width: 0.08333rem;
  height: 100%;
  background-image: linear-gradient(to bottom, #5f52e3 50%, transparent 50%);
  /* 创建水平虚线 */
  background-size: 0.83333rem 0.83333rem;
  /* 每个虚线段的长度为 10px，间距为 20px */
  background-repeat: repeat-y;
  /* 垂直重复虚线 */
  opacity: 0.25;
  margin: 0 auto;
}
.lesson-card .lesson-text-item-title[data-v-a3d8fb46] {
  font-weight: 400;
  font-size: 2.16667rem;
  height: 2.83333rem;
  line-height: 2.83333rem;
  padding-top: 0.66667rem;
}
.lesson-card .lesson-text-item-content[data-v-a3d8fb46] {
  font-size: var(--font-size-medium);
  height: 4.41667rem;
  line-height: 4.41667rem;
  padding-top: 1.16667rem;
  font-family: var(--font-family-title);
}
.lesson-card .lesson-text-item .text-flip[data-v-a3d8fb46] {
  max-width: 22.5rem;
}
.lesson-card .lesson-switch-teacher[data-v-a3d8fb46] {
  position: absolute;
  right: 0.41667rem;
  bottom: 3.25rem;
  width: 2.58333rem;
  height: 2.58333rem;
  border-radius: 50%;
  background-color: rgba(95, 82, 227, 0.65);
  border: 0.16667rem solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(1.875rem);
}
.lesson-card .lesson-switch-teacher[data-v-a3d8fb46]::after {
  content: '';
  position: absolute;
  width: 1.91667rem;
  height: 1.91667rem;
  left: 0.33333rem;
  top: 0.33333rem;
  margin: auto;
  background: url('data:image/webp;base64,UklGRl4DAABXRUJQVlA4WAoAAAAwAAAALwAALwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMbwEAAC8vwAsQB2IgbZv4N73tNzH/88+wbSTFKfmYH+uQA6ADMm40wABAASRABWRABRRAAxQEJAQEQAUAQIQHkBAAAAewAQAD8AM2IAGgogJkybabSBIz9zAzST3M06Sz/1U1uGpKA98R/XfgNpIiRetlPngEAAAAAAAAAAAAAAAAAAAAAAAAAIB79XOp1deqqvlGH+uywz7vq7m3dV7rwsvBapcHg/UutwY7Xa4NDrrsDm522Rjcbbvg4HGbp1e96fs+HV1xGhERERERERERERERERFP5n6nqqqqqqqqqqqqqqoa3tHyxF60dDZll5ZhclsdvkzZefFkZj8AAAAAAPAf/PP6PvMxxwtTfO446+af/ZactfR8YsstHU2ezD/LzMzMzMzMzMzMzMzMzNOr9X1mr6+OnrV5NOzS5s5go8uNwV6X/cH1LtuD213WBg+7rAxezWLKd2fwru2Ch1d90Nyni99u68K31h/70q+6///7RwIA') no-repeat center center;
  background-size: 100% 100%;
}
.lesson-card .lesson-start[data-v-a3d8fb46]::before {
  content: '';
  width: 6.66667rem;
  height: 6.66667rem;
  background: url('../webp/<EMAIL>') no-repeat center;
  background-size: 100% 100%;
  position: absolute;
  right: -1.83333rem;
  top: -2.5rem;
}
.lesson-start[data-v-a3d8fb46] {
  width: 14.08333rem;
  height: 6rem;
  position: relative;
}
.lesson-start[data-v-a3d8fb46]::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 14.08333rem 6rem;
}
.lesson-bing-class[data-v-a3d8fb46] {
  width: 14.08333rem;
  height: 6rem;
  position: relative;
}
.lesson-bing-class[data-v-a3d8fb46]::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 14.08333rem 6rem;
}
.text-flip[data-v-efce4d6b] {
  display: inline-block;
  height: 100%;
  overflow: hidden;
  transition: transform 1s ease;
}
.timetable-item-left[data-v-7dd5bc1e] {
  width: 2.91667rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.timetable-icon-wait[data-v-7dd5bc1e] {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  background: radial-gradient(0% 50% at 50% 50%, #8b80fa 2%, #b7b0ff 100%);
  opacity: 0.3;
  border-radius: 50%;
}
.timetable-icon-finished[data-v-7dd5bc1e] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.timetable-icon-finished i[data-v-7dd5bc1e] {
  width: 0.5rem;
  height: 0.5rem;
  background: radial-gradient(0% 50% at 50% 50%, #8b80fa 2%, #b7b0ff 100%);
  opacity: 0.3;
  border-radius: 50%;
}
.timetable-icon-finished i + i[data-v-7dd5bc1e] {
  margin-top: 0.33333rem;
}
.timetable-item-doing[data-v-7dd5bc1e] {
  font-size: var(--font-size-small);
  color: #29da80;
  line-height: 2.5rem;
  text-align: center;
  font-family: var(--font-family-title);
}
.timetable-item-doing[data-v-7dd5bc1e]::before {
  content: '';
  display: inline-block;
  width: 1.08333rem;
  height: 1.08333rem;
  background: #29da80;
  border-radius: 50%;
  vertical-align: top;
  margin-top: 0.66667rem;
  margin-right: 0.75rem;
}
.timetable-item-time[data-v-7dd5bc1e] {
  color: var(--text-color-secondary);
  font-size: var(--font-size-small);
  font-weight: 400;
  line-height: 2.5rem;
  text-align: right;
}
.timetable-item-text[data-v-7dd5bc1e] {
  font-size: 2.16667rem;
  line-height: 2.83333rem;
  font-weight: 400;
  height: 2.83333rem;
  flex: 1;
}
.timetable-item-right[data-v-7dd5bc1e] {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
}
.timetable-item[data-v-7dd5bc1e] {
  height: 4.75rem;
  display: flex;
  align-items: center;
  padding: 0 2.33333rem 0 1.16667rem;
}
.timetable-item.timetable-item-current .timetable-item-text[data-v-7dd5bc1e] {
  font-weight: 600;
}
.timetable-item.timetable-item-done[data-v-7dd5bc1e] {
  color: var(--text-color-disabled);
}
.timetable-item.timetable-item-done .timetable-item-time[data-v-7dd5bc1e] {
  color: var(--text-color-disabled);
}
.timetable-split[data-v-94c74dbb] {
  width: 23.41667rem;
  height: 2.33333rem;
  position: relative;
  margin: 0 auto;
  overflow: hidden;
}
.timetable-split[data-v-94c74dbb]::before {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  content: '';
  display: block;
  height: 0.08333rem;
  background-image: linear-gradient(to right, #5f52e3 50%, transparent 50%);
  /* 创建水平虚线 */
  background-size: 0.78rem 0.78rem;
  /* 每个虚线段的长度为 10px，间距为 20px */
  background-repeat: repeat-x;
  /* 垂直重复虚线 */
  opacity: 0.25;
}
.timetable-split[data-v-94c74dbb]::after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  content: '';
  display: block;
  height: 0.08333rem;
  background-image: linear-gradient(to right, #5f52e3 50%, transparent 50%);
  /* 创建水平虚线 */
  background-size: 0.78rem 0.78rem;
  /* 每个虚线段的长度为 10px，间距为 20px */
  background-repeat: repeat-x;
  /* 垂直重复虚线 */
  opacity: 0.25;
}
.timetable-split-bg[data-v-94c74dbb] {
  width: 15.66667rem;
  height: 2.16667rem;
  background: #5f52e3;
  opacity: 0.2;
  filter: blur(3.33333rem);
  margin: 0.08333rem auto;
}
.timetable-split-text[data-v-94c74dbb] {
  font-family: var(--font-family-title);
  height: 2.25rem;
  font-size: 1.25rem;
  color: rgba(95, 82, 227, 0.45);
  line-height: 2.25rem;
  letter-spacing: 0.16667rem;
  text-align: center;
  margin-top: -2.08333rem;
}
.flex {
  display: flex;
}
.flex-jc {
  justify-content: center;
}
.flex-ac {
  align-items: center;
}
.flex-v {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.active-scale {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.active-scale:active {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.pressed-scale {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.pressed-scale.pressed {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制文本为2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}
.timetable {
  width: 31.25rem;
  background: linear-gradient(180deg, #f1f1fc 0%, #f1e7f9 100%);
  border-radius: 3.16667rem;
  backdrop-filter: blur(1.66667rem);
}
.timetable-header {
  color: #fff;
  width: 25.16667rem;
  height: 4.83333rem;
  background-image: url('../webp/<EMAIL>');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 0 auto;
}
.timetable-header-title {
  font-weight: 600;
  letter-spacing: 0.16667rem;
  text-align: center;
  padding-top: 0.41667rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  height: 1.58333rem;
  line-height: 1.58333rem;
}
.timetable-header-title::before {
  content: '';
  width: 0.75rem;
  height: 0.75rem;
  background-image: url('data:image/webp;base64,UklGRjADAABXRUJQVlA4WAoAAAAwAAAAEwAAEwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMQgEAAC8TwAQQ/6AoktSoOV7494GZaIkCRCho24ZJz/hDGZcBmggmaYT+P3lMUds2cMr978IwhQACIAANx+7vj8x4r5V3qwpdKhQIDUqBcDhEBikGuRsKIAYA2rbx1rY1PIRMEP9/2chOX4jovyK3bZuszpP0C2Zm5n6qtdapZ2Z+7rpKhIhIVLtXD4R3KQClINLw7gL5TOnIUL7VXgnLlWLwPsSUC1LHXLHk5P26rqv3rSr3dEPyq7PaWLeFBDf1I5YzbNZoKZWxPpzlMxLC7lejhBBCmtUnwFoRkndGinkW0ji//4xlnhep3Wu0n0aKZflxmNqX1WophNR2C0fBqaeGZbVGa2PXBoZ6JoQj+s1Z6zYfD0BqTG84GtIG9YQv0wf7G/IZU9yPDP8IgZzhoaO5fCj5IN434ufvtRl+OR+JiMancwY=');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 0.75rem;
}
.timetable-header-title::after {
  content: '';
  width: 0.75rem;
  height: 0.75rem;
  background-image: url('data:image/webp;base64,UklGRjADAABXRUJQVlA4WAoAAAAwAAAAEwAAEwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMQgEAAC8TwAQQ/6AoktSoOV7494GZaIkCRCho24ZJz/hDGZcBmggmaYT+P3lMUds2cMr978IwhQACIAANx+7vj8x4r5V3qwpdKhQIDUqBcDhEBikGuRsKIAYA2rbx1rY1PIRMEP9/2chOX4jovyK3bZuszpP0C2Zm5n6qtdapZ2Z+7rpKhIhIVLtXD4R3KQClINLw7gL5TOnIUL7VXgnLlWLwPsSUC1LHXLHk5P26rqv3rSr3dEPyq7PaWLeFBDf1I5YzbNZoKZWxPpzlMxLC7lejhBBCmtUnwFoRkndGinkW0ji//4xlnhep3Wu0n0aKZflxmNqX1WophNR2C0fBqaeGZbVGa2PXBoZ6JoQj+s1Z6zYfD0BqTG84GtIG9YQv0wf7G/IZU9yPDP8IgZzhoaO5fCj5IN434ufvtRl+OR+JiMancwY=');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-left: 0.75rem;
}
.timetable-header-class {
  width: 18.33333rem;
  height: 1.91667rem;
  font-size: var(--font-size-extra-small);
  line-height: 1.91667rem;
  letter-spacing: 0.25rem;
  text-align: center;
  padding-top: 0.5rem;
  font-family: var(--font-family-title);
  margin: auto;
}
.timetable-list {
  height: 56.33333rem;
  margin: 2.33333rem 0;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
}
.timetable-rest {
  height: 61rem;
  background: url('../webp/<EMAIL>') no-repeat center 20.66667rem;
  background-size: 9.91667rem 8.58333rem;
  position: relative;
}
.timetable-rest-text {
  height: 3.16667rem;
  font-weight: 400;
  font-family: var(--font-family);
  font-size: var(--font-size-small);
  color: var(--text-color-secondary);
  line-height: 3.16667rem;
  text-align: center;
  position: relative;
  top: 50%;
  transform: translateY(0.3rem);
}
.timetable-list-fish {
  width: 1.83333rem;
  height: 2.33333rem;
  background: url('data:image/webp;base64,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') no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  left: 1.70833rem;
  transition: top 3s ease;
  margin-top: 1.125rem;
  opacity: 1;
}
.timetable-list-fish.finish {
  transition: opacity 1s ease;
  opacity: 0;
}
.timetable-footer {
  width: 100%;
  height: 8.83333rem;
  background: linear-gradient(180deg, rgba(95, 82, 227, 0.2) 0%, rgba(95, 82, 227, 0.45) 100%);
  border-radius: 0rem 0rem 3.16667rem 3.16667rem;
  text-align: center;
}
.timetable-footer-title {
  font-family: var(--font-family-title);
  font-size: 2.33333rem;
  font-weight: 500;
  color: var(--text-color);
  padding-top: 1.16667rem;
  height: 2.83333rem;
  line-height: 2.83333rem;
}
.timetable-footer-list {
  padding-top: 0.75rem;
  font-weight: 400;
  font-size: var(--font-size-small);
  color: var(--text-color);
  line-height: 2.5rem;
}
.timetable-footer-list span + span {
  margin-left: 1.58333rem;
}
.date-solar-terms[data-v-d98115ca] {
  padding-top: 3.25rem;
}
.date-solar-terms-time[data-v-d98115ca] {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-number);
  font-size: var(--font-size-large);
  line-height: 5.41667rem;
  text-align: center;
}
.date-solar-terms-time-divider[data-v-d98115ca] {
  margin-left: 1.5rem;
}
.date-solar-terms-time-minute[data-v-d98115ca] {
  margin-left: 2rem;
}
.date-solar-terms-date[data-v-d98115ca] {
  height: 2.83333rem;
  font-weight: 600;
  font-size: 2.16667rem;
  line-height: 2.83333rem;
  text-align: center;
  letter-spacing: 0.16667rem;
}
.class-reminder[data-v-6d40fd98] {
  height: 40.58333rem;
  background: linear-gradient(180deg, rgba(241, 241, 252, 0) 0%, #f1effb 38%, #f1e7f9 100%);
  border-radius: 3.16667rem;
  backdrop-filter: blur(1.66667rem);
}
.class-reminder-title[data-v-6d40fd98] {
  font-family: var(--font-family-title);
  font-size: 2.5rem;
  line-height: 2.83333rem;
  height: 2.83333rem;
  text-align: center;
  padding-top: 7.75rem;
}
.class-reminder-content[data-v-6d40fd98] {
  height: 2.83333rem;
  font-weight: 400;
  font-size: 2.16667rem;
  color: var(--text-color-secondary);
  line-height: 2.83333rem;
  text-align: center;
  padding-top: 1.58333rem;
}
.class-reminder-countdown[data-v-6d40fd98] {
  width: 16rem;
  height: 16rem;
  font-family: var(--font-family-number);
  font-weight: 900;
  font-size: 3.25rem;
  color: #261a99;
  line-height: 16rem;
  text-align: center;
  background: url('../webp/<EMAIL>') no-repeat center center;
  background-size: 100% 100%;
  margin: 1.25rem auto 0;
}
.class-reminder-countdown.countdown-done[data-v-6d40fd98] {
  color: var(--text-color-disabled);
}
.class-reminder-header[data-v-6d40fd98] {
  text-align: center;
  margin-top: 2.25rem;
}
.class-reminder-close-btn[data-v-6d40fd98] {
  display: inline-block;
  align-items: center;
  justify-content: center;
  height: 3rem;
  cursor: pointer;
}
.class-reminder-close-text[data-v-6d40fd98] {
  font-weight: 400;
  font-size: 2.16667rem;
  line-height: 3rem;
  height: 3rem;
  letter-spacing: 0.33333rem;
  color: #5f52e3;
  vertical-align: top;
}
.class-reminder-close-icon[data-v-6d40fd98] {
  width: 3rem;
  height: 3rem;
  display: inline-block;
  border-radius: 0.66667rem;
  background-color: rgba(255, 255, 255, 0.4);
  margin-right: 0.91667rem;
  vertical-align: top;
  opacity: 0.75;
}
.class-reminder-close-icon[data-v-6d40fd98]::after {
  content: '';
  display: inline-block;
  width: 2.83333rem;
  height: 2.83333rem;
  vertical-align: top;
  background: url('data:image/webp;base64,UklGRpwDAABXRUJQVlA4WAoAAAAwAAAAQwAAQwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMrQEAAC9DwBAQn2CmbRtv95CNP56WhuK2bRylrXvD37dJQds2kvfc8Qd4VE5V0LaRk9zvxeEBPL7HH4gVv169fV/+0cD5SaOWqxVySeHDQRQNk2ENKDoguY3kSJqZ9d4k///XXWT15FZc9xDRfwZuGykaJXvMNwtfAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAICbqrvpHX3W67aoqrqf3dBXVT1ti8bMnXHbGfq2b8N9Z+p2ZgaYGOG6Ia4Z49ggR0Zpvv8kbJbFIEfm+QhXP9+9eMjbqqCk7YhOzn62x07Uoyoo6hFBo6yJFWXNzShrekdZa4hNjFvRVk+Nc/ayq21sVd088V92xetvbfmH2uO0bfFp3hBfns//2cZIAAA=') no-repeat center center;
  background-size: 100% 100%;
  border: 0.08333rem solid rgba(255, 255, 255, 0.06);
}
.weather[data-v-3ae2df63] {
  display: flex;
  align-items: flex-end;
}
.weather-icon[data-v-3ae2df63] {
  width: 5.66667rem;
  height: 5.66667rem;
}
.weather-current-temperature[data-v-3ae2df63] {
  height: 4.75rem;
  font-family: var(--font-family-number);
  font-weight: 900;
  font-size: 4.08333rem;
  line-height: 4.75rem;
  margin-bottom: -0.16667rem;
  position: relative;
}
.weather-current-temperature[data-v-3ae2df63]::before {
  content: '';
  display: block;
  width: 0.33333rem;
  height: 0.33333rem;
  border-radius: 50%;
  border: 0.41667rem solid var(--text-color-regular);
  position: absolute;
  top: -0.41667rem;
  right: -0.83333rem;
}
.weather-detail[data-v-3ae2df63] {
  margin-left: 1.33333rem;
}
.weather-desc[data-v-3ae2df63] {
  height: 2.16667rem;
  font-weight: 600;
  font-size: 1.58333rem;
  line-height: 2.16667rem;
  margin-bottom: -0.16667rem;
}
.weather-desc-temperature[data-v-3ae2df63] {
  height: 2.41667rem;
  font-weight: 600;
  font-size: 1.75rem;
  line-height: 2.41667rem;
  margin-bottom: 0.08333rem;
}
/**
 * Swiper 8.4.7
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2023 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: January 30, 2023
 */

@font-face{font-family:swiper-icons;src:url('data:application/font-woff;charset=utf-8;base64, 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');font-weight:400;font-style:normal}:root{--swiper-theme-color:#007aff}.swiper{margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;box-sizing:content-box}.swiper-android .swiper-slide,.swiper-wrapper{transform:translate3d(0rem,0,0)}.swiper-pointer-events{touch-action:pan-y}.swiper-pointer-events.swiper-vertical{touch-action:pan-x}.swiper-slide{flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform}.swiper-slide-invisible-blank{visibility:hidden}.swiper-autoheight,.swiper-autoheight .swiper-slide{height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden .swiper-slide{transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d,.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:100rem}.swiper-3d .swiper-cube-shadow,.swiper-3d .swiper-slide,.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top,.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top{position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}.swiper-3d .swiper-slide-shadow{background:rgba(0,0,0,.15)}.swiper-3d .swiper-slide-shadow-left{background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-right{background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-top{background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-bottom{background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode>.swiper-wrapper>.swiper-slide{scroll-snap-align:start start}.swiper-horizontal.swiper-css-mode>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-vertical.swiper-css-mode>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:0.08333rem;width:var(--swiper-centered-offset-after)}.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:0.08333rem;height:var(--swiper-centered-offset-after)}.swiper-centered>.swiper-wrapper>.swiper-slide{scroll-snap-align:center center;scroll-snap-stop:always}.swiper-pagination{position:absolute;text-align:center;transition:.3s opacity;transform:translate3d(0,0,0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-pagination-disabled>.swiper-pagination,.swiper-pagination.swiper-pagination-disabled{display:none!important}.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal,.swiper-pagination-custom,.swiper-pagination-fraction{bottom:0.83333rem;left:0;width:100%}.swiper-pagination-bullets-dynamic{overflow:hidden;font-size:0}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transform:scale(.33);position:relative}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{transform:scale(.33)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{transform:scale(.33)}.swiper-pagination-bullet{width:var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,0.66667rem));height:var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,0.66667rem));display:inline-block;border-radius:50%;background:var(--swiper-pagination-bullet-inactive-color,#000);opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;-webkit-appearance:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-bullet:only-child{display:none!important}.swiper-pagination-bullet-active{opacity:var(--swiper-pagination-bullet-opacity, 1);background:var(--swiper-pagination-color,var(--swiper-theme-color))}.swiper-pagination-vertical.swiper-pagination-bullets,.swiper-vertical>.swiper-pagination-bullets{right:0.83333rem;top:50%;transform:translate3d(0rem,-50%,0)}.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{margin:var(--swiper-pagination-bullet-vertical-gap,0.5rem) 0;display:block}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{top:50%;transform:translateY(-50%);width:0.66667rem}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{display:inline-block;transition:.2s transform,.2s top}.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 var(--swiper-pagination-bullet-horizontal-gap,0.33333rem)}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{left:50%;transform:translateX(-50%);white-space:nowrap}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s left}.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s right}.swiper-pagination-progressbar{background:rgba(0,0,0,.25);position:absolute}.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--swiper-pagination-color,var(--swiper-theme-color));position:absolute;left:0;top:0;width:100%;height:100%;transform:scale(0);transform-origin:left top}.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{transform-origin:right top}.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite{width:100%;height:0.33333rem;left:0;top:0}.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-vertical>.swiper-pagination-progressbar{width:0.33333rem;height:100%;left:0;top:0}.swiper-pagination-lock{display:none}:root{--swiper-navigation-size:3.66667rem}.swiper-button-next,.swiper-button-prev{position:absolute;top:50%;width:calc(var(--swiper-navigation-size)/ 44 * 27);height:var(--swiper-navigation-size);margin-top:calc(0rem - (var(--swiper-navigation-size)/ 2));z-index:10;cursor:pointer;display:flex;align-items:center;justify-content:center;color:var(--swiper-navigation-color,var(--swiper-theme-color))}.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-next.swiper-button-hidden,.swiper-button-prev.swiper-button-hidden{opacity:0;cursor:auto;pointer-events:none}.swiper-navigation-disabled .swiper-button-next,.swiper-navigation-disabled .swiper-button-prev{display:none!important}.swiper-button-next:after,.swiper-button-prev:after{font-family:swiper-icons;font-size:var(--swiper-navigation-size);text-transform:none!important;letter-spacing:0;font-variant:initial;line-height:1}.swiper-button-prev,.swiper-rtl .swiper-button-next{left:0.83333rem;right:auto}.swiper-button-prev:after,.swiper-rtl .swiper-button-next:after{content:'prev'}.swiper-button-next,.swiper-rtl .swiper-button-prev{right:0.83333rem;left:auto}.swiper-button-next:after,.swiper-rtl .swiper-button-prev:after{content:'next'}.swiper-button-lock{display:none}.default-state-img[data-v-93f72a09] {
  width: 10.25rem;
  height: 8.83333rem;
}
.key-app-box[data-v-ac40943c] {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3.16667rem;
  backdrop-filter: blur(0.83333rem);
}
.key-app-box .default-state-box[data-v-ac40943c] {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.key-app[data-v-ac40943c] {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 3.83333rem 1.33333rem;
  box-sizing: border-box;
}
.apps-item[data-v-ac40943c] {
  width: 7.33333rem;
  height: 7.33333rem;
  text-align: center;
  cursor: pointer;
  margin-right: 0.66667rem;
  margin-bottom: 3.83333rem;
  font-size: 0;
}
.apps-item__icon[data-v-ac40943c] {
  width: 4.83333rem;
  height: 4.83333rem;
}
.apps-item__name[data-v-ac40943c] {
  width: 7.66667rem;
  text-align: center;
  font-size: 1rem;
  margin-top: 0.5rem;
}
.apps-item[data-v-ac40943c]:nth-of-type(3n) {
  margin-right: 0;
}
.count-down-box[data-v-a4b827c4] {
  width: 100%;
  height: 100%;
}
.count-down[data-v-a4b827c4] {
  display: flex;
  width: 100%;
  height: 100%;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 100% 100%;
  padding-right: 1.5rem;
  box-sizing: border-box;
}
.count-down .count-down-left[data-v-a4b827c4] {
  width: 24.83333rem;
  text-align: center;
  padding-top: 2.5rem;
}
.count-down .count-down-left .count-down-left-name[data-v-a4b827c4] {
  font-family: DOUYINSANSBOLD, DOUYINSANSBOLD, sans-serif;
  font-size: 1.91667rem;
  color: #000000;
  margin-bottom: 2.25rem;
}
.count-down .count-down-left .count-down-left-time[data-v-a4b827c4] {
  font-size: 1.58333rem;
  color: rgba(0, 0, 0, 0.45);
  padding-right: 0.66667rem;
}
.count-down .count-down-right[data-v-a4b827c4] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000000;
  font-family: DOUYINSANSBOLD, DOUYINSANSBOLD, sans-serif;
}
.count-down .count-down-right .count-down-right-day[data-v-a4b827c4] {
  font-size: 3.5rem;
  margin-right: 0.75rem;
}
.count-down .count-down-right .count-down-right-unit[data-v-a4b827c4] {
  font-size: 1.91667rem;
}
.count-down-defalut[data-v-a4b827c4] {
  width: 100%;
  height: 100%;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 100% 100%;
}
.one-word[data-v-c307386f] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-family: DOUYINSANSBOLD, DOUYINSANSBOLD, sans-serif;
  color: #000000;
  padding: 0rem 2.5rem;
  box-sizing: border-box;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 100% 100%;
}
.one-word .one-word-chinese[data-v-c307386f] {
  font-size: 1.91667rem;
  margin-bottom: 0.41667rem;
}
.one-word .one-word-chinese-two[data-v-c307386f] {
  font-size: 1.58333rem;
}
.one-word .one-word-english[data-v-c307386f] {
  font-size: 1.25rem;
  line-height: 1.91667rem;
}
.one-word-default[data-v-c307386f] {
  width: 100%;
  height: 100%;
  background: url('../webp/<EMAIL>') no-repeat;
  background-size: 100% 100%;
}
.elite-school-detail[data-v-596239a3] {
  background: #ffffff;
  border-radius: 3.33333rem;
  padding: 3rem;
}
.elite-school-detail .slide-intro[data-v-596239a3] {
  width: 79.66667rem;
  height: 5.66667rem;
  font-size: 1.83333rem;
  line-height: 2.91667rem;
  letter-spacing: 0.04167rem;
  color: rgba(0, 0, 0, 0.65);
  box-sizing: border-box;
  margin: 2.66667rem auto 0;
}
.close-icon[data-v-596239a3] {
  cursor: pointer;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -3.33333rem;
  width: 3.08333rem;
  height: 3.08333rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: radial-gradient(0% 48% at 50% 50%, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.25) 100%);
  border-radius: 0.66667rem;
  border: 0.08333rem solid rgba(255, 255, 255, 0.06);
}
.close-icon .el-icon[data-v-596239a3] {
  font-size: 1.66667rem;
  color: white;
  font-weight: 600;
}
.elite-school-swiper[data-v-596239a3] {
  width: 79.66667rem;
  height: 44.83333rem;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide[data-v-596239a3] {
  min-width: 1.66667rem;
  width: 100%;
  height: 100%;
  text-align: center;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .img-slide-box[data-v-596239a3] {
  min-width: 1.66667rem;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .img-slide-box .img-slide[data-v-596239a3] {
  height: 100%;
  object-fit: cover;
  border-radius: 2.5rem;
}
.elite-school-swiper .elite-school-slide .video-slide[data-v-596239a3] {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .video-slide .video[data-v-596239a3] {
  width: 100%;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .video-slide .play-icon[data-v-596239a3] {
  width: 10.83333rem;
  height: 10.83333rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.elite-school-swiper[data-v-596239a3] .swiper-pagination-bullet-active {
  background: white !important;
}
.detail-dialog {
  width: 95.75rem !important;
  background: transparent !important;
  box-shadow: unset !important;
}
.detail-dialog .el-dialog__header {
  display: none;
}
.elite-school[data-v-821522d5] {
  width: 100%;
  height: 100%;
  position: relative;
  background: white;
  padding: 0.83333rem;
  box-sizing: border-box;
  border-radius: 3.16667rem;
}
.elite-school .corner-cut[data-v-821522d5] {
  width: 25rem;
  position: absolute;
  z-index: 1;
  bottom: 0rem;
  left: 0rem;
}
.elite-school .slide-intro[data-v-821522d5] {
  width: 25rem;
  height: 3rem;
  line-height: 3rem;
  position: absolute;
  z-index: 2;
  left: 0rem;
  bottom: 0rem;
  background: white;
  padding: 0 0.83333rem;
  box-sizing: border-box;
  border-radius: 0rem 4rem 0rem 3.16667rem;
  font-weight: 500;
  font-size: 1.41667rem;
  color: #000000;
  text-align: center;
}
.elite-school-swiper[data-v-821522d5] {
  width: 100%;
  height: 100%;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide[data-v-821522d5] {
  min-width: 1.66667rem;
  width: 100%;
  height: 100%;
  cursor: pointer;
  text-align: center;
}
.elite-school-swiper .elite-school-slide .img-slide-box[data-v-821522d5] {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .img-slide-box .img-slide[data-v-821522d5] {
  width: 100%;
  object-fit: cover;
}
.elite-school-swiper .elite-school-slide .video-slide[data-v-821522d5] {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  background: black;
  border-radius: 2.5rem;
  overflow: hidden;
}
.elite-school-swiper .elite-school-slide .video-slide .video[data-v-821522d5] {
  width: 100%;
}
.elite-school-swiper .elite-school-slide .video-slide .play-icon[data-v-821522d5] {
  width: 4.16667rem;
  height: 4.16667rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.elite-school-default[data-v-821522d5] {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f1eefb;
  border-radius: 1.66667rem;
}
.ai-class-summary-box[data-v-1e323687] {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('../webp/<EMAIL>');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 3.16667rem;
  padding: 0.83333rem;
  box-sizing: border-box;
}
.ai-class-summary-box .defaul-img[data-v-1e323687] {
  position: absolute;
  width: 8.08333rem;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.ai-class-summary-box .xmind-img[data-v-1e323687] {
  position: absolute;
  height: 8.33333rem;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0.83333rem;
}
.review-template[data-v-ebc3f05d] {
  width: 112rem;
  height: 40.58333rem;
  position: relative;
}
.review-swiper[data-v-ebc3f05d] {
  width: 100%;
  height: 100%;
}
.review-swiper .review-slide[data-v-ebc3f05d] {
  width: 100%;
  height: 100%;
}
.apps-item[data-v-ebc3f05d] {
  margin: 1.16667rem;
}
.vgl-item[data-v-ebc3f05d] {
  transition: none;
}
.vgl-layout[data-v-ebc3f05d] {
  height: 40.58333rem;
  position: absolute;
  top: -2.33333rem;
  right: -2.33333rem;
  bottom: -2.33333rem;
  left: -2.33333rem;
}
.grid-item[data-v-ebc3f05d] {
  width: 100%;
  height: 100%;
}
.workbench-header[data-v-5d4ef817] {
  width: var(--view-width);
  height: 11.75rem;
  margin: 0 auto var(--block-margin-small);
  position: relative;
}
.workbench-body[data-v-5d4ef817] {
  width: var(--view-width);
  margin: 0 auto;
  display: flex;
}
.workbench-left[data-v-5d4ef817] {
  width: 112rem;
  margin-right: var(--block-margin-default);
}
.workbench-lesson[data-v-5d4ef817] {
  width: 100%;
  height: 22.75rem;
}
.workbench-app[data-v-5d4ef817] {
  margin-top: var(--block-margin-default);
  position: relative;
}
.class-reminder[data-v-5d4ef817] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.workbench-right[data-v-5d4ef817] {
  flex: 1;
}
.workbench-weather[data-v-5d4ef817] {
  position: absolute;
  top: 2.75rem;
  left: 0.58333rem;
}
