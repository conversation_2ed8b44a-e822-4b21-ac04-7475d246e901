import { d as defineComponent, r as ref, w as watch, h as withDirectives, v as vShow, b as openBlock, m as createElementBlock, j as createBaseVNode, B as renderSlot, D as createCommentVNode, l as normalizeStyle, H as toDisplayString } from "./bootstrap.ab073eb8.js";
import { h as useZIndex } from "./index.1c1fd1ce.js";
import { _ as _export_sfc } from "./index.f30fa4a1.js";
const _hoisted_1 = { class: "dialog-title" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    visible: { type: <PERSON>olean },
    title: {},
    showClose: { type: Boolean },
    containerStyle: {}
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const zIndex = ref(2e3);
    const onCLickClose = () => {
      emits("close");
    };
    const { nextZIndex } = useZIndex();
    watch(
      () => props.visible,
      (val) => {
        if (val) {
          zIndex.value = nextZIndex();
        }
      },
      {
        immediate: true
      }
    );
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createElementBlock("div", {
        class: "dialog-wrapper flex flex-ac flex-jc",
        style: normalizeStyle({
          zIndex: zIndex.value
        })
      }, [
        createBaseVNode("div", {
          class: "dialog-container",
          style: normalizeStyle(props.containerStyle)
        }, [
          renderSlot(_ctx.$slots, "header", {}, () => [
            createBaseVNode("div", _hoisted_1, toDisplayString(_ctx.title), 1)
          ], true),
          renderSlot(_ctx.$slots, "default", {}, void 0, true),
          props.showClose ? (openBlock(), createElementBlock("div", {
            key: 0,
            class: "dialog-close",
            onClick: onCLickClose
          })) : createCommentVNode("", true)
        ], 4)
      ], 4)), [
        [vShow, _ctx.visible]
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_fecd2eab_lang = "";
const Dialog = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-fecd2eab"]]);
export {
  Dialog as D
};
