//
//  CefVersion.h
//  CefViewCore
//
//  Created by <PERSON><PERSON> on 2025/1/19.
//  This file was generated during CMake configuring.
//  Do not edit this file directly by manual.
//  Edit the CefVersion.h.in and then re-config project with CMake.
//

#ifndef CefVersion_h
#define CefVersion_h
#pragma once

// clang-format off
#define CEF_VERSION "126.2.19+ga5d51ba+chromium-126.0.6478.183"
#define CEF_VERSION_MAJOR 126
#define CEF_VERSION_MINOR 2
#define CEF_VERSION_PATCH 19
#define CEF_COMMIT_NUMBER 3020
#define CEF_COMMIT_HASH "a5d51ba9db7ee74a523dc247bd09ec91ba4d7446"
// clang-format on

#endif // CefVersion
