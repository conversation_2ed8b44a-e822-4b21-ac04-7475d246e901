import { $ as $post } from "./axios.234f7bed.js";
const sysUrl = "/zhhb-system";
function getSchoolByCode(data) {
  return $post(`${sysUrl}/v1/school/get_by_code`, data);
}
function getCampusList(data) {
  return $post(`${sysUrl}/v1/school/get_campus_list`, data);
}
function getClassList(data) {
  return $post(`${sysUrl}/v1/school/get_class_lists`, data);
}
function getClassInfo(data) {
  return $post(`${sysUrl}/v1/school/get_class_info`, data, {
    closeErrorToast: true
  });
}
function getClassStudentList(data, options) {
  return $post(`${sysUrl}/v1/tool/class_students`, data, options);
}
export {
  getCampusList as a,
  getClassList as b,
  getClassInfo as c,
  getClassStudentList as d,
  getSchoolByCode as g
};
