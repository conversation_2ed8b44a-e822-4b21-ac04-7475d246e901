#include "QtPathClipper.h"
#include <QtMath>

QPainterPath QtPathClipper::clipOpenPath(const QPainterPath& path, const QRectF& clipRect)
{
    qDebug() << "QtPathClipper: Clipping path with" << path.elementCount() << "elements against rect" << clipRect;

    // 实现"减法"操作：保留路径中不与矩形相交的部分
    QPainterPath result = subtractRect(path, clipRect);

    qDebug() << "QtPathClipper: Result has" << result.elementCount() << "elements";
    return result;
}

QList<QPainterPath> QtPathClipper::toSubpaths(const QPainterPath& path)
{
    QList<QPainterPath> subpaths;
    if (path.isEmpty()) return subpaths;

    QPainterPath current;
    for (int i = 0; i < path.elementCount(); ++i) {
        const QPainterPath::Element& e = path.elementAt(i);
        switch (e.type) {
        case QPainterPath::MoveToElement:
            if (current.elementCount() > 1) {
                subpaths += current;
            }
            current = QPainterPath();
            current.moveTo(e);
            break;
        case QPainterPath::LineToElement:
            current.lineTo(e);
            break;
        case QPainterPath::CurveToElement:
            current.cubicTo(e, path.elementAt(i + 1), path.elementAt(i + 2));
            i += 2;
            break;
        case QPainterPath::CurveToDataElement:
            Q_ASSERT(!"toSubpaths(), bad element type");
            break;
        }
    }

    if (current.elementCount() > 1) {
        subpaths << current;
    }

    return subpaths;
}

bool QtPathClipper::isVertical(Edge edge)
{
    return edge == Left || edge == Right;
}

template<QtPathClipper::Edge edge>
bool QtPathClipper::compare(const QPointF& p, qreal t)
{
    switch (edge) {
    case Left:
        return p.x() < t;
    case Right:
        return p.x() > t;
    case Top:
        return p.y() < t;
    default: // Bottom
        return p.y() > t;
    }
}

template<QtPathClipper::Edge edge>
QPointF QtPathClipper::intersectLine(const QPointF& a, const QPointF& b, qreal t)
{
    QLineF line(a, b);
    switch (edge) {
    case Left:
    case Right:
        return line.pointAt((t - a.x()) / (b.x() - a.x()));
    default: // Top or Bottom
        return line.pointAt((t - a.y()) / (b.y() - a.y()));
    }
}

void QtPathClipper::addLine(QPainterPath& path, const QLineF& line)
{
    if (path.elementCount() > 0) {
        path.lineTo(line.p1());
    } else {
        path.moveTo(line.p1());
    }
    path.lineTo(line.p2());
}

template<QtPathClipper::Edge edge>
void QtPathClipper::clipLine(const QPointF& a, const QPointF& b, qreal t, QPainterPath& result)
{
    bool outA = compare<edge>(a, t);
    bool outB = compare<edge>(b, t);

    if (outA && outB) {
        return; // 完全在外部
    }

    if (outA) {
        // a在外部，b在内部
        addLine(result, QLineF(intersectLine<edge>(a, b, t), b));
    } else if (outB) {
        // a在内部，b在外部
        addLine(result, QLineF(a, intersectLine<edge>(a, b, t)));
    } else {
        // 都在内部
        addLine(result, QLineF(a, b));
    }
}

template<QtPathClipper::Edge edge>
QPainterPath QtPathClipper::clip(const QPainterPath& path, qreal t)
{
    QPainterPath result;

    for (int i = 1; i < path.elementCount(); ++i) {
        const QPainterPath::Element& element = path.elementAt(i);
        Q_ASSERT(!element.isMoveTo());

        if (element.isLineTo()) {
            clipLine<edge>(path.elementAt(i-1), path.elementAt(i), t, result);
        } else {
            // 对于曲线，简化为线段处理
            clipLine<edge>(path.elementAt(i-1), path.elementAt(i+2), t, result);
            i += 2;
        }
    }

    // 处理闭合路径的最后一段（对于开放路径，这通常不需要）
    int last = path.elementCount() - 1;
    if (QPointF(path.elementAt(last)) != QPointF(path.elementAt(0))) {
        clipLine<edge>(path.elementAt(last), path.elementAt(0), t, result);
    }

    return result;
}

QPainterPath QtPathClipper::intersectPath(const QPainterPath& path, const QRectF& rect)
{
    QList<QPainterPath> subpaths = toSubpaths(path);
    QPainterPath result;
    result.setFillRule(path.fillRule());

    qDebug() << "QtPathClipper: Processing" << subpaths.size() << "subpaths";

    for (int i = 0; i < subpaths.size(); ++i) {
        QPainterPath subPath = subpaths.at(i);
        QRectF bounds = subPath.boundingRect();

        qDebug() << "  Subpath" << i << "bounds:" << bounds << "vs clip rect:" << rect;

        if (bounds.intersects(rect)) {
            // 依次对四个边进行切割
            if (bounds.left() < rect.left()) {
                subPath = clip<Left>(subPath, rect.left());
                bounds = subPath.boundingRect();
            }
            if (bounds.right() > rect.right()) {
                subPath = clip<Right>(subPath, rect.right());
                bounds = subPath.boundingRect();
            }
            if (bounds.top() < rect.top()) {
                subPath = clip<Top>(subPath, rect.top());
                bounds = subPath.boundingRect();
            }
            if (bounds.bottom() > rect.bottom()) {
                subPath = clip<Bottom>(subPath, rect.bottom());
            }

            if (subPath.elementCount() > 1) {
                result.addPath(subPath);
                qDebug() << "    Added clipped subpath with" << subPath.elementCount() << "elements";
            }
        }
    }

    return result;
}

QPainterPath QtPathClipper::subtractRect(const QPainterPath& path, const QRectF& rect)
{
    qDebug() << "QtPathClipper: Subtracting rect" << rect << "from path";

    QList<QPainterPath> subpaths = toSubpaths(path);
    QPainterPath result;
    result.setFillRule(path.fillRule());

    for (int i = 0; i < subpaths.size(); ++i) {
        QPainterPath subPath = subpaths.at(i);
        QRectF bounds = subPath.boundingRect();

        qDebug() << "  Processing subpath" << i << "bounds:" << bounds;

        // 对于高度为0的路径（水平线），我们需要特殊处理
        if (bounds.height() == 0) {
            // 这是一条水平线，检查Y坐标是否在橡皮擦范围内
            qreal lineY = bounds.y();
            if (lineY >= rect.top() && lineY <= rect.bottom()) {
                // 水平线在橡皮擦的Y范围内，需要进行X方向的切割
                qDebug() << "    Horizontal line at Y=" << lineY << "intersects eraser Y range [" << rect.top() << "," << rect.bottom() << "]";

                // 检查X方向是否相交
                if (bounds.right() >= rect.left() && bounds.left() <= rect.right()) {
                    qDebug() << "    Horizontal line also intersects eraser X range, performing clipping";

                    // 将水平线分割成在矩形外的部分
                    QList<QPainterPath> outsideParts = clipPathOutsideRect(subPath, rect);

                    for (const QPainterPath& part : outsideParts) {
                        if (part.elementCount() > 1) {
                            // 使用连续路径合并，避免插入不必要的MoveToElement
                            addPathContinuously(result, part);
                            qDebug() << "      Added outside part with" << part.elementCount() << "elements";
                        }
                    }
                } else {
                    // X方向不相交，保留整条线
                    result.addPath(subPath);
                    qDebug() << "    Horizontal line outside eraser X range, keeping entire path";
                }
            } else {
                // Y坐标不在橡皮擦范围内，保留整条线
                result.addPath(subPath);
                qDebug() << "    Horizontal line outside eraser Y range, keeping entire path";
            }
        } else if (bounds.width() == 0) {
            // 这是一条垂直线，检查X坐标是否在橡皮擦范围内
            qreal lineX = bounds.x();
            if (lineX >= rect.left() && lineX <= rect.right()) {
                // 垂直线在橡皮擦的X范围内，需要进行Y方向的切割
                qDebug() << "    Vertical line at X=" << lineX << "intersects eraser X range [" << rect.left() << "," << rect.right() << "]";

                // 检查Y方向是否相交
                if (bounds.bottom() >= rect.top() && bounds.top() <= rect.bottom()) {
                    qDebug() << "    Vertical line also intersects eraser Y range, performing clipping";

                    // 将垂直线分割成在矩形外的部分
                    QList<QPainterPath> outsideParts = clipPathOutsideRect(subPath, rect);

                    for (const QPainterPath& part : outsideParts) {
                        if (part.elementCount() > 1) {
                            // 使用连续路径合并，避免插入不必要的MoveToElement
                            addPathContinuously(result, part);
                            qDebug() << "      Added outside part with" << part.elementCount() << "elements";
                        }
                    }
                } else {
                    // Y方向不相交，保留整条线
                    result.addPath(subPath);
                    qDebug() << "    Vertical line outside eraser Y range, keeping entire path";
                }
            } else {
                // X坐标不在橡皮擦范围内，保留整条线
                result.addPath(subPath);
                qDebug() << "    Vertical line outside eraser X range, keeping entire path";
            }
        } else {
            // 普通的2D路径，使用原有逻辑
            if (!bounds.intersects(rect)) {
                // 子路径完全在矩形外，直接保留
                result.addPath(subPath);
                qDebug() << "    Subpath outside rect, keeping entire path";
            } else if (rect.contains(bounds)) {
                // 子路径完全在矩形内，完全删除
                qDebug() << "    Subpath completely inside rect, removing";
            } else {
                // 子路径与矩形部分相交，需要切割
                qDebug() << "    Subpath intersects rect, performing clipping";

                // 将路径分割成在矩形外的部分
                QList<QPainterPath> outsideParts = clipPathOutsideRect(subPath, rect);
                qDebug() << "      clipPathOutsideRect returned" << outsideParts.size() << "parts";

                for (const QPainterPath& part : outsideParts) {
                    qDebug() << "        Part has" << part.elementCount() << "elements, bounds:" << part.boundingRect();
                    if (part.elementCount() > 1) {
                        // 使用连续路径合并，避免插入不必要的MoveToElement
                        addPathContinuously(result, part);
                        qDebug() << "        Added outside part with" << part.elementCount() << "elements";
                    } else {
                        qDebug() << "        Skipped part with only" << part.elementCount() << "elements";
                    }
                }
            }
        }
    }

    return result;
}

QList<QPainterPath> QtPathClipper::clipPathOutsideRect(const QPainterPath& path, const QRectF& rect)
{
    QList<QPainterPath> result;

    // 将路径转换为点序列
    QList<QPointF> points;
    for (int i = 0; i < path.elementCount(); ++i) {
        const QPainterPath::Element& element = path.elementAt(i);
        if (element.type == QPainterPath::MoveToElement ||
            element.type == QPainterPath::LineToElement) {
            points.append(QPointF(element.x, element.y));
        }
    }

    if (points.size() < 2) {
        return result;
    }

    // 遍历所有线段，收集在矩形外的部分
    QPainterPath currentPath;
    bool inPath = false;

    for (int i = 0; i < points.size() - 1; ++i) {
        QPointF p1 = points[i];
        QPointF p2 = points[i + 1];

        bool p1Inside = rect.contains(p1);
        bool p2Inside = rect.contains(p2);

        if (!p1Inside && !p2Inside) {
            // 两点都在外部，检查线段是否穿过矩形
            if (!lineIntersectsRect(p1, p2, rect)) {
                // 线段完全在外部
                if (!inPath) {
                    currentPath = QPainterPath();
                    currentPath.moveTo(p1);
                    inPath = true;
                }
                currentPath.lineTo(p2);
            } else {
                // 线段穿过矩形，需要分割
                if (inPath) {
                    if (currentPath.elementCount() > 1) {
                        result.append(currentPath);
                    }
                    inPath = false;
                }
                // 这里可以进一步实现线段与矩形的精确交点计算
            }
        } else if (!p1Inside && p2Inside) {
            // p1在外部，p2在内部
            if (!inPath) {
                currentPath = QPainterPath();
                currentPath.moveTo(p1);
                inPath = true;
            }
            // 计算与矩形边界的交点
            QPointF intersection = findRectIntersection(p1, p2, rect);
            currentPath.lineTo(intersection);

            if (currentPath.elementCount() > 1) {
                result.append(currentPath);
            }
            inPath = false;
        } else if (p1Inside && !p2Inside) {
            // p1在内部，p2在外部
            QPointF intersection = findRectIntersection(p1, p2, rect);
            currentPath = QPainterPath();
            currentPath.moveTo(intersection);
            currentPath.lineTo(p2);
            inPath = true;
        } else {
            // 两点都在内部，结束当前路径
            if (inPath) {
                if (currentPath.elementCount() > 1) {
                    result.append(currentPath);
                }
                inPath = false;
            }
        }
    }

    // 添加最后一个路径
    if (inPath && currentPath.elementCount() > 1) {
        result.append(currentPath);
    }

    return result;
}

bool QtPathClipper::lineIntersectsRect(const QPointF& p1, const QPointF& p2, const QRectF& rect)
{
    // 简单的线段与矩形相交测试
    QLineF line(p1, p2);

    // 检查与四条边的相交
    QLineF edges[4] = {
        QLineF(rect.topLeft(), rect.topRight()),      // 上边
        QLineF(rect.topRight(), rect.bottomRight()),  // 右边
        QLineF(rect.bottomRight(), rect.bottomLeft()), // 下边
        QLineF(rect.bottomLeft(), rect.topLeft())     // 左边
    };

    for (int i = 0; i < 4; ++i) {
        QPointF intersection;
        if (line.intersects(edges[i], &intersection) == QLineF::BoundedIntersection) {
            return true;
        }
    }

    return false;
}

QPointF QtPathClipper::findRectIntersection(const QPointF& p1, const QPointF& p2, const QRectF& rect)
{
    QLineF line(p1, p2);

    // 检查与四条边的相交，返回第一个找到的交点
    QLineF edges[4] = {
        QLineF(rect.topLeft(), rect.topRight()),      // 上边
        QLineF(rect.topRight(), rect.bottomRight()),  // 右边
        QLineF(rect.bottomRight(), rect.bottomLeft()), // 下边
        QLineF(rect.bottomLeft(), rect.topLeft())     // 左边
    };

    for (int i = 0; i < 4; ++i) {
        QPointF intersection;
        if (line.intersects(edges[i], &intersection) == QLineF::BoundedIntersection) {
            return intersection;
        }
    }

    // 如果没有找到交点，返回较近的点
    return rect.contains(p1) ? p2 : p1;
}

// 显式实例化模板函数
template bool QtPathClipper::compare<QtPathClipper::Left>(const QPointF&, qreal);
template bool QtPathClipper::compare<QtPathClipper::Right>(const QPointF&, qreal);
template bool QtPathClipper::compare<QtPathClipper::Top>(const QPointF&, qreal);
template bool QtPathClipper::compare<QtPathClipper::Bottom>(const QPointF&, qreal);

template QPointF QtPathClipper::intersectLine<QtPathClipper::Left>(const QPointF&, const QPointF&, qreal);
template QPointF QtPathClipper::intersectLine<QtPathClipper::Right>(const QPointF&, const QPointF&, qreal);
template QPointF QtPathClipper::intersectLine<QtPathClipper::Top>(const QPointF&, const QPointF&, qreal);
template QPointF QtPathClipper::intersectLine<QtPathClipper::Bottom>(const QPointF&, const QPointF&, qreal);

template void QtPathClipper::clipLine<QtPathClipper::Left>(const QPointF&, const QPointF&, qreal, QPainterPath&);
template void QtPathClipper::clipLine<QtPathClipper::Right>(const QPointF&, const QPointF&, qreal, QPainterPath&);
template void QtPathClipper::clipLine<QtPathClipper::Top>(const QPointF&, const QPointF&, qreal, QPainterPath&);
template void QtPathClipper::clipLine<QtPathClipper::Bottom>(const QPointF&, const QPointF&, qreal, QPainterPath&);

template QPainterPath QtPathClipper::clip<QtPathClipper::Left>(const QPainterPath&, qreal);
template QPainterPath QtPathClipper::clip<QtPathClipper::Right>(const QPainterPath&, qreal);
template QPainterPath QtPathClipper::clip<QtPathClipper::Top>(const QPainterPath&, qreal);
template QPainterPath QtPathClipper::clip<QtPathClipper::Bottom>(const QPainterPath&, qreal);

void QtPathClipper::addPathContinuously(QPainterPath& target, const QPainterPath& source)
{
    if (source.isEmpty()) {
        return;
    }

    // 如果目标路径为空，直接添加清理后的源路径
    if (target.isEmpty()) {
        QPainterPath cleanedSource = removeDuplicatePoints(source);
        target.addPath(cleanedSource);
        return;
    }

    // 获取目标路径的最后一个点
    QPointF targetLastPoint = target.currentPosition();

    // 清理源路径中的重复点
    QPainterPath cleanedSource = removeDuplicatePoints(source);

    // 获取清理后源路径的第一个点
    QPointF sourceFirstPoint;
    bool foundFirstPoint = false;

    for (int i = 0; i < cleanedSource.elementCount(); ++i) {
        QPainterPath::Element element = cleanedSource.elementAt(i);
        if (element.type == QPainterPath::MoveToElement ||
            element.type == QPainterPath::LineToElement) {
            sourceFirstPoint = QPointF(element.x, element.y);
            foundFirstPoint = true;
            break;
        }
    }

    if (!foundFirstPoint) {
        // 源路径没有有效点，直接返回
        return;
    }

    // 检查两个路径是否连续（距离很近）
    qreal distance = QLineF(targetLastPoint, sourceFirstPoint).length();
    const qreal CONTINUITY_THRESHOLD = 2.0; // 增加到2像素的连续性阈值，处理精度问题

    if (distance <= CONTINUITY_THRESHOLD) {
        // 路径连续，跳过源路径的第一个MoveToElement，直接连接
        qDebug() << "[PATH_MERGE] Paths are continuous (distance=" << distance << "), merging without MoveTo";

        bool skipFirst = true;
        QPointF lastAddedPoint = targetLastPoint;

        for (int i = 0; i < cleanedSource.elementCount(); ++i) {
            QPainterPath::Element element = cleanedSource.elementAt(i);
            QPointF currentPoint(element.x, element.y);

            if (skipFirst && element.type == QPainterPath::MoveToElement) {
                skipFirst = false;
                continue; // 跳过第一个MoveToElement
            }

            // 检查是否与上一个点重复
            if (QLineF(lastAddedPoint, currentPoint).length() < 0.5) {
                qDebug() << "[PATH_MERGE] Skipping duplicate point at" << currentPoint;
                continue; // 跳过重复点
            }

            switch (element.type) {
            case QPainterPath::MoveToElement:
                target.moveTo(element.x, element.y);
                lastAddedPoint = currentPoint;
                break;
            case QPainterPath::LineToElement:
                target.lineTo(element.x, element.y);
                lastAddedPoint = currentPoint;
                break;
            case QPainterPath::CurveToElement:
                if (i + 2 < cleanedSource.elementCount()) {
                    QPainterPath::Element cp1 = cleanedSource.elementAt(i + 1);
                    QPainterPath::Element cp2 = cleanedSource.elementAt(i + 2);
                    target.cubicTo(element.x, element.y, cp1.x, cp1.y, cp2.x, cp2.y);
                    lastAddedPoint = QPointF(cp2.x, cp2.y);
                    i += 2; // 跳过控制点
                }
                break;
            default:
                break;
            }
        }
    } else {
        // 路径不连续，需要插入MoveToElement
        qDebug() << "[PATH_MERGE] Paths are not continuous (distance=" << distance << "), adding with MoveTo";
        target.addPath(cleanedSource);
    }
}

QPainterPath QtPathClipper::removeDuplicatePoints(const QPainterPath& path)
{
    if (path.isEmpty()) {
        return path;
    }

    QPainterPath result;
    result.setFillRule(path.fillRule());

    QPointF lastPoint;
    bool hasLastPoint = false;
    const qreal DUPLICATE_THRESHOLD = 0.1; // 0.1像素的重复点阈值

    for (int i = 0; i < path.elementCount(); ++i) {
        QPainterPath::Element element = path.elementAt(i);
        QPointF currentPoint(element.x, element.y);

        // 检查是否与上一个点重复
        bool isDuplicate = false;
        if (hasLastPoint) {
            qreal distance = QLineF(lastPoint, currentPoint).length();
            isDuplicate = (distance < DUPLICATE_THRESHOLD);
        }

        if (!isDuplicate) {
            switch (element.type) {
            case QPainterPath::MoveToElement:
                result.moveTo(currentPoint);
                lastPoint = currentPoint;
                hasLastPoint = true;
                break;
            case QPainterPath::LineToElement:
                result.lineTo(currentPoint);
                lastPoint = currentPoint;
                hasLastPoint = true;
                break;
            case QPainterPath::CurveToElement:
                if (i + 2 < path.elementCount()) {
                    QPainterPath::Element cp1 = path.elementAt(i + 1);
                    QPainterPath::Element cp2 = path.elementAt(i + 2);
                    result.cubicTo(currentPoint, QPointF(cp1.x, cp1.y), QPointF(cp2.x, cp2.y));
                    lastPoint = QPointF(cp2.x, cp2.y);
                    hasLastPoint = true;
                    i += 2; // 跳过控制点
                }
                break;
            default:
                break;
            }
        } else {
            qDebug() << "[PATH_CLEAN] Removed duplicate point at" << currentPoint << "(distance=" << QLineF(lastPoint, currentPoint).length() << ")";
        }
    }

    return result;
}
